package com.moego.server.customer.params;

import jakarta.annotation.Nullable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SearchCustomerIdsParam {

    /**
     * companyId or businessId is required
     */
    @Nullable
    private Long companyId;

    /**
     * companyId or businessId is required
     */
    @Nullable
    private Integer businessId;

    @Nullable
    private String keyword;

    private List<String> clientTypes;
}
