package com.moego.server.grooming.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.SubscriptionConst;
import com.moego.idl.models.smart_scheduler.v1.SmartScheduleSettingModel;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingResponse;
import com.moego.idl.service.smart_scheduler.v1.SmartScheduleSettingServiceGrpc;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.params.LocationParams;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.params.BatchSSParams;
import com.moego.server.grooming.service.params.GetAvailableSlotParams;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SmartScheduleV3ServiceTest {

    @Mock
    OBBusinessService businessService;

    @Mock
    SmartScheduleService smartScheduleService;

    @Mock
    IBusinessStaffClient iBusinessStaffClient;

    @Mock
    IBusinessBusinessClient iBusinessBusinessClient;

    @Mock
    IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Mock
    SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceBlockingStub smartScheduleSettingServiceBlockingStub;

    @InjectMocks
    SmartScheduleV3Service service;

    @Test
    void getAvailableTimeSlot() {
        GetAvailableSlotParams params = new GetAvailableSlotParams();
        List<TimeSlot> resultList = service.getAvailableTimeSlot(null, params);
        assertEquals(0, resultList.size());
        params.setTimeSlots(List.of());
        resultList = service.getAvailableTimeSlot(null, params);
        assertEquals(0, resultList.size());

        BatchSSParams ssParams = new BatchSSParams();
        ssParams.setIsSmartScheduleEnable(false);
        params.setTimeSlots(List.of(
                TimeSlot.builder().end(30).start(0).build(),
                TimeSlot.builder().end(65).start(30).build(),
                TimeSlot.builder().end(105).start(65).build()));
        params.setServiceDuration(35);
        params.setNeedCount(3);
        resultList = service.getAvailableTimeSlot(ssParams, params);
        assertEquals(2, resultList.size());
        params.setServiceDuration(5);
        params.setNeedCount(service.COUNT_FOR_AVAILABLE_TIME_SLOT_NEED);
        resultList = service.getAvailableTimeSlot(ssParams, params);
        assertEquals(3, resultList.size());

        ssParams.setIsSmartScheduleEnable(true);
        ssParams.setLocations(Map.of(1L, new LocationParams("1", "2", "")));
        params.setDriverTimeCalculator((slots, lat, lng, duration) -> {});
        params.setLocationId(1L);
        params.setServiceDuration(15);
        doReturn(true).when(smartScheduleService).meetMaxDrivingTimeAndDist(any(), any());
        params.setNeedCount(3);
        resultList = service.getAvailableTimeSlot(ssParams, params);
        assertEquals(2, resultList.size());
        verify(smartScheduleService, times(2)).meetMaxDrivingTimeAndDist(any(), any());
        params.setNeedCount(1);
        resultList = service.getAvailableTimeSlot(ssParams, params);
        assertEquals(1, resultList.size());
        verify(smartScheduleService, times(3)).meetMaxDrivingTimeAndDist(any(), any());
        params.setNeedCount(service.COUNT_FOR_AVAILABLE_TIME_SLOT_NEED);
        resultList = service.getAvailableTimeSlot(ssParams, params);
        assertEquals(2, resultList.size());
    }

    @Test
    void getCACDAndSSFlag() {
        Integer businessId = 1;

        // 非 ob
        doReturn(GetSmartScheduleSettingResponse.newBuilder()
                        .setSmartScheduleSetting(SmartScheduleSettingModel.newBuilder()
                                .setServiceAreaEnable(false)
                                .build())
                        .build())
                .when(smartScheduleSettingServiceBlockingStub)
                .getSmartScheduleSetting(any());
        doReturn(OBBusinessInfoDTO.builder()
                        .businessMode(SubscriptionConst.BUSINESS_TYPE_SALON)
                        .companyId(1)
                        .build())
                .when(iBusinessBusinessClient)
                .getBusinessInfoForOB(any());
        List<Boolean> result = service.getCACDAndSSFlag(false, businessId);
        assertFalse(result.get(0));
        assertFalse(result.get(1));

        doReturn(GetSmartScheduleSettingResponse.newBuilder()
                        .setSmartScheduleSetting(SmartScheduleSettingModel.newBuilder()
                                .setServiceAreaEnable(true)
                                .build())
                        .build())
                .when(smartScheduleSettingServiceBlockingStub)
                .getSmartScheduleSetting(any());
        doReturn(OBBusinessInfoDTO.builder()
                        .companyId(1)
                        .businessMode(SubscriptionConst.BUSINESS_TYPE_MOBILE)
                        .build())
                .when(iBusinessBusinessClient)
                .getBusinessInfoForOB(any());
        result = service.getCACDAndSSFlag(false, businessId);
        assertTrue(result.get(0));
        assertTrue(result.get(1));

        // ob
        MoeBusinessBookOnline businessBookOnline = new MoeBusinessBookOnline();
        businessBookOnline.setIsNeedAddress(CommonConstant.DISABLE);
        businessBookOnline.setServiceAreaEnable(CommonConstant.DISABLE);
        businessBookOnline.setSmartScheduleEnable(CommonConstant.DISABLE);
        doReturn(OBBusinessInfoDTO.builder()
                        .companyId(1)
                        .businessMode(SubscriptionConst.BUSINESS_TYPE_MOBILE)
                        .build())
                .when(iBusinessBusinessClient)
                .getBusinessInfoForOB(any());
        doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(any());
        result = service.getCACDAndSSFlag(true, businessId);
        assertFalse(result.get(0));
        assertFalse(result.get(1));

        businessBookOnline.setIsNeedAddress(CommonConstant.ENABLE);
        doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(any());
        result = service.getCACDAndSSFlag(true, businessId);
        assertFalse(result.get(0));
        assertFalse(result.get(1));

        businessBookOnline.setServiceAreaEnable(CommonConstant.ENABLE);
        doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(any());
        result = service.getCACDAndSSFlag(true, businessId);
        assertTrue(result.get(0));
        assertFalse(result.get(1));

        businessBookOnline.setSmartScheduleEnable(CommonConstant.ENABLE);
        doReturn(businessBookOnline).when(businessService).getSettingInfoByBusinessId(any());
        result = service.getCACDAndSSFlag(true, businessId);
        assertTrue(result.get(0));
        assertTrue(result.get(1));

        doReturn(OBBusinessInfoDTO.builder()
                        .companyId(1)
                        .businessMode(SubscriptionConst.BUSINESS_TYPE_SALON)
                        .build())
                .when(iBusinessBusinessClient)
                .getBusinessInfoForOB(any());
        result = service.getCACDAndSSFlag(true, businessId);
        assertFalse(result.get(0));
        assertFalse(result.get(1));
    }

    @Test
    void getStaffPerDayWorkingTime() {
        doReturn(Map.of(
                        10,
                                Map.of(
                                        "1", List.of(new TimeRangeDto(1, 5), new TimeRangeDto(10, 15)),
                                        "2", List.of(new TimeRangeDto(1, 3), new TimeRangeDto(5, 7))),
                        20, Map.of("1", List.of(new TimeRangeDto(1, 2)))))
                .when(iBusinessStaffClient)
                .queryStaffWorkingHourRange(any(), any(), any(), any());
        Map<String, Map<Integer, List<TimeRangeDto>>> result =
                service.getStaffPerDayWorkingTime(false, 1, List.of(), LocalDate.now(), LocalDate.now());
        assertEquals(2, result.size());
        assertEquals(2, result.get("1").size());
        assertEquals(2, result.get("1").get(10).size());
        assertEquals(1, result.get("1").get(10).get(0).getStartTime());
        assertEquals(5, result.get("1").get(10).get(0).getEndTime());
        assertEquals(10, result.get("1").get(10).get(1).getStartTime());
        assertEquals(15, result.get("1").get(10).get(1).getEndTime());
        assertEquals(1, result.get("1").get(20).size());
        assertEquals(1, result.get("1").get(20).get(0).getStartTime());
        assertEquals(2, result.get("1").get(20).get(0).getEndTime());

        assertEquals(1, result.get("2").size());
        assertEquals(2, result.get("2").get(10).size());
        assertEquals(1, result.get("2").get(10).get(0).getStartTime());
        assertEquals(3, result.get("2").get(10).get(0).getEndTime());
        assertEquals(5, result.get("2").get(10).get(1).getStartTime());
        assertEquals(7, result.get("2").get(10).get(1).getEndTime());
    }

    @Test
    void cacdResult2StaffWorkingTime() {
        Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> cacdResult = Map.of(
                "2020-02-01",
                        Map.of(
                                1, List.of(new WorkingTimeAndAreaDto(100, 1100, 10)),
                                2, List.of(new WorkingTimeAndAreaDto(200, 2200, 20))),
                "2020-02-02",
                        Map.of(
                                1, List.of(new WorkingTimeAndAreaDto(100, 1100, 10)),
                                3,
                                        List.of(
                                                new WorkingTimeAndAreaDto(300, 3300, 30),
                                                new WorkingTimeAndAreaDto(3300, 33300, 30))));
        Map<String, Map<Integer, List<TimeRangeDto>>> result = service.cacdResult2StaffWorkingTime(cacdResult);
        assertEquals(2, result.size());

        assertEquals(2, result.get("2020-02-01").size());
        assertEquals(1, result.get("2020-02-01").get(1).size());
        assertEquals(100, result.get("2020-02-01").get(1).get(0).getStartTime());
        assertEquals(1100, result.get("2020-02-01").get(1).get(0).getEndTime());
        assertEquals(1, result.get("2020-02-01").get(2).size());
        assertEquals(200, result.get("2020-02-01").get(2).get(0).getStartTime());
        assertEquals(2200, result.get("2020-02-01").get(2).get(0).getEndTime());

        assertEquals(2, result.get("2020-02-02").size());
        assertEquals(1, result.get("2020-02-02").get(1).size());
        assertEquals(100, result.get("2020-02-02").get(1).get(0).getStartTime());
        assertEquals(1100, result.get("2020-02-02").get(1).get(0).getEndTime());
        assertEquals(2, result.get("2020-02-02").get(3).size());
        assertEquals(300, result.get("2020-02-02").get(3).get(0).getStartTime());
        assertEquals(3300, result.get("2020-02-02").get(3).get(0).getEndTime());
        assertEquals(3300, result.get("2020-02-02").get(3).get(1).getStartTime());
        assertEquals(33300, result.get("2020-02-02").get(3).get(1).getEndTime());
    }

    @Test
    void filterExactStartTime() {
        List<TimeSlot> timeSlots = List.of(
                TimeSlot.builder().start(0).end(5).build(),
                TimeSlot.builder().start(5).end(15).build(),
                TimeSlot.builder().start(15).end(20).build());
        List<TimeSlot> result = service.filterExactStartTime(null, timeSlots);
        assertEquals(3, result.size());
        result = service.filterExactStartTime(List.of(10), timeSlots);
        assertEquals(1, result.size());
        assertEquals(10, result.get(0).getStart());
        assertEquals(15, result.get(0).getEnd());

        result = service.filterExactStartTime(List.of(3, 10), timeSlots);
        assertEquals(2, result.size());
        assertEquals(3, result.get(0).getStart());
        assertEquals(5, result.get(0).getEnd());
        assertEquals(10, result.get(1).getStart());
        assertEquals(15, result.get(1).getEnd());
    }

    @Test
    void testFilterStartTime() {
        List<TimeSlot> timeSlots = List.of(
                TimeSlot.builder().start(0).end(5).build(),
                TimeSlot.builder().start(5).end(15).build(),
                TimeSlot.builder().start(15).end(20).build());
        List<TimeSlot> result = service.filterStartTime(10, timeSlots);
        assertEquals(2, result.size());
        assertEquals(10, result.get(0).getStart());
        assertEquals(15, result.get(0).getEnd());
        assertEquals(15, result.get(1).getStart());
        assertEquals(20, result.get(1).getEnd());
    }

    @Test
    void filterEndTime() {
        List<TimeSlot> timeSlots = List.of(
                TimeSlot.builder().start(0).end(5).build(),
                TimeSlot.builder().start(5).end(15).build(),
                TimeSlot.builder().start(15).end(20).build());
        List<TimeSlot> result = service.filterEndTime(10, timeSlots);
        assertEquals(2, result.size());
        assertEquals(0, result.get(0).getStart());
        assertEquals(5, result.get(0).getEnd());
        assertEquals(5, result.get(1).getStart());
        assertEquals(10, result.get(1).getEnd());
    }

    @Test
    void dateCheck() {
        LocalDate curDate = LocalDate.of(2023, 12, 12);
        assertTrue(service.dateCheck(curDate, null, List.of(LocalDate.of(2023, 12, 11), LocalDate.of(2023, 12, 12))));
        assertTrue(service.dateCheck(curDate, null, List.of(LocalDate.of(2023, 12, 12))));
        assertFalse(service.dateCheck(curDate, null, List.of(LocalDate.of(2023, 12, 11), LocalDate.of(2023, 12, 13))));

        assertTrue(service.dateCheck(curDate, List.of(1, 2), null));
        assertTrue(service.dateCheck(curDate, List.of(2), null));
        assertFalse(service.dateCheck(curDate, List.of(1, 3), null));
    }
}
