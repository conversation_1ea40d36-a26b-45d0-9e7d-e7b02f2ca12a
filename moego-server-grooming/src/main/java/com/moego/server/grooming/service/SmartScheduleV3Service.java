package com.moego.server.grooming.service;

import static com.moego.common.utils.WeekUtil.getDayOfWeek;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.SMART_SCHEDULE_BUFFER_TIME;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.fillServiceAddressV2;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.filterNonWorkingTime;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.staffPerDayWorkingTimeIntersection;
import static com.moego.server.grooming.service.utils.SmartScheduleUtil.timeSlotListIntersection;

import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.utils.DateUtil;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingRequest;
import com.moego.idl.service.smart_scheduler.v1.SmartScheduleSettingServiceGrpc;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.client.IBusinessSmartSchedulingClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.OBBusinessInfoDTO;
import com.moego.server.business.dto.StaffSmartScheduleSettingDTO;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.params.GetSmartScheduleSettingParams;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.LocationParams;
import com.moego.server.business.params.StaffCACDBatchRequest;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.mapperbean.MoeBusinessBookOnline;
import com.moego.server.grooming.service.ob.OBBusinessService;
import com.moego.server.grooming.service.params.BatchSSParams;
import com.moego.server.grooming.service.params.GetAvailableSlotParams;
import com.moego.server.grooming.service.params.GetFreeSlotParams;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * 提供通用的 SS（Smart Schedule）功能服务。
 * 为了平衡效率和成本，将 SS 功能分为两个步骤：
 *      1.getStaffPerDayFreeTimeSlot：获取指定日期范围内，所有员工，在除 驾驶耗时+服务耗时 外的其他条件下满足的 freeTimeSlot。
 *      2.getAvailableTimeSlot：检查 timeSlot 是否符合 驾驶耗时+服务耗时 条件。该方法可能调用收费的谷歌接口，需要在各种场景中灵活调用，力求减少冗余的 API 调用。
 * 为了支持批量 SS，减少数据请求次数，提供 initSmartScheduleParams 方法，批量请求数据并缓存到 SmartScheduleParams 中，以避免重复查询。
 * 综上，批量 SS 操作流程如下：
 *      1.initBatchSSParams：请求（批量）外部依赖接口，请求结果缓存到 BatchSSParams 中。
 *      2.getStaffPerDayFreeTimeSlot：无任何外部依赖。针对单个 SS 场景，获取每一天每个员工的 freeTimeSlot。
 *      3.getAvailableTimeSlot：仅可能依赖谷歌接口。返回 timeSlot 列表中满足驾驶耗时条件若干 timeSlot。
 */
@Slf4j
@Service
public class SmartScheduleV3Service {

    @Autowired
    private IBusinessStaffClient iBusinessStaffClient;

    @Autowired
    private IBusinessServiceAreaClient iBusinessServiceAreaClient;

    @Autowired
    private MoePetDetailService moePetDetailService;

    @Autowired
    private SmartScheduleService smartScheduleService;

    @Autowired
    private OBBusinessService businessService;

    @Autowired
    private IBusinessSmartSchedulingClient iBusinessSmartSchedulingClient;

    @Autowired
    private IBusinessBusinessClient iBusinessBusinessClient;

    @Autowired
    private SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceBlockingStub
            smartScheduleSettingServiceBlockingStub;

    public final int COUNT_FOR_AVAILABLE_TIME_SLOT_NEED = -1;

    /**
     *
     * @param staffIdList 需要参与 ss 计算的职工
     * @param startDate 批量 SS 起始时间
     * @param endDate 批量 SS 结束时间
     * @param locations 批量 SS 目标地址 <locationId, location>. cacd enable 或 ss enable 时有效
     */
    public BatchSSParams initBatchSSParams(
            Boolean isOB,
            Integer businessId,
            List<Integer> staffIdList,
            LocalDate startDate,
            LocalDate endDate,
            Map<Long, LocationParams> locations) {
        BatchSSParams ssParams = new BatchSSParams();
        ssParams.setBusinessId(businessId);
        ssParams.setIsOB(isOB);
        ssParams.setStaffIdList(staffIdList);

        List<Boolean> cacdAndSSFlag = getCACDAndSSFlag(isOB, businessId);
        boolean isCACDEnable = cacdAndSSFlag.get(0);
        boolean isSmartScheduleEnable = cacdAndSSFlag.get(1);
        ssParams.setIsCACDEnable(isCACDEnable);
        ssParams.setIsSmartScheduleEnable(isSmartScheduleEnable);
        if (isSmartScheduleEnable) {
            Map<Integer, StaffSmartScheduleSettingDTO> staffSsSettingMap =
                    iBusinessSmartSchedulingClient.getStaffSmartScheduleSettingMap(
                            new GetSmartScheduleSettingParams(businessId, staffIdList));
            ssParams.setStaffSsSettingMap(staffSsSettingMap);
        }

        // 过滤掉无效的 location
        locations = locations.entrySet().stream()
                .filter(e -> {
                    LocationParams locationParams = e.getValue();
                    return locationParams != null && locationParams.getLat() != null && locationParams.getLng() != null;
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        ssParams.setLocations(locations);

        ssParams.setStartDate(startDate);
        ssParams.setEndDate(endDate);

        // 已有预约信息
        List<SmartScheduleGroomingDetailsDTO> existPetDetailInfoList =
                moePetDetailService.getServiceList(businessId, startDate, endDate, staffIdList);
        Map<Integer, CustomerAddressDto> existAppointmentCustomerAddress =
                moePetDetailService.getCustomerAddress(existPetDetailInfoList);
        ssParams.setExistPetDetailInfoList(existPetDetailInfoList);
        ssParams.setExistAppointmentCustomerAddress(existAppointmentCustomerAddress);

        // 获取员工每天工作时间
        Map<String, Map<Integer, List<TimeRangeDto>>> staffPerDayWorkingTime =
                getStaffPerDayWorkingTime(isOB, businessId, staffIdList, startDate, endDate);
        ssParams.setStaffPerDayWorkingTime(staffPerDayWorkingTime);

        // 员工 cacd 信息
        if (isCACDEnable) {
            Map<Long, Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>>> staffCACDResult =
                    iBusinessStaffClient.queryStaffCACDBatch(new StaffCACDBatchRequest(
                            businessId, staffIdList, startDate.toString(), endDate.toString(), locations));
            Map<Long, Map<String, Map<Integer, List<TimeRangeDto>>>> cacdWorkingTime =
                    staffCACDResult.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey, entry -> cacdResult2StaffWorkingTime(entry.getValue())));
            ssParams.setCacdWorkingTime(cacdWorkingTime);
        }

        return ssParams;
    }

    /**
     * 返回指定时间范围内，指定员工，满足要求的空闲时间段。
     * @return <date, <staffId, List<TimeSlot>>>  长度为 0 的 List<TimeSlot> 会从结果中过滤掉
     */
    public Map<String, Map<Integer, List<TimeSlot>>> getStaffPerDayFreeTimeSlot(
            BatchSSParams ssParams, GetFreeSlotParams params) {
        Map<String, Map<Integer, List<TimeRangeDto>>> staffPerDayWorkingTime = ssParams.getStaffPerDayWorkingTime();

        // cacd 过滤
        if (ssParams.getIsCACDEnable()) {
            staffPerDayWorkingTime = staffPerDayWorkingTimeIntersection(
                    staffPerDayWorkingTime, ssParams.getCacdWorkingTime().get(params.getLocationId()));
        }

        Map<String, Map<Integer, List<TimeSlot>>> result = new HashMap<>();
        for (LocalDate date = params.getStartDateTime().toLocalDate();
                !date.isAfter(params.getEndDateTime().toLocalDate());
                date = date.plusDays(1)) {
            // 检查是否是偏好日期
            if (!dateCheck(date, params.getExpectDayOfWeekList(), params.getExpectDateList())) {
                continue;
            }
            String dateStr = date.toString();
            for (Integer staffId : ssParams.getStaffIdList()) {
                // 该职工当天工作时间
                List<TimeRangeDto> curWorkingTime = staffPerDayWorkingTime
                        .getOrDefault(dateStr, new HashMap<>())
                        .get(staffId);
                if (CollectionUtils.isEmpty(curWorkingTime)) {
                    continue;
                }
                // 该职工当天预约情况
                List<SmartScheduleGroomingDetailsDTO> petDetailInfoList = ssParams.getExistPetDetailInfoList().stream()
                        .filter(srv -> srv.getStaffId().equals(staffId)
                                && (StringUtils.hasText(srv.getStartDate())
                                                ? srv.getStartDate()
                                                : srv.getAppointmentDate())
                                        .equals(dateStr))
                        .filter(srv -> CollectionUtils.isEmpty(params.getFilterAppointmentIdList())
                                || !params.getFilterAppointmentIdList().contains(srv.getGroomingId()))
                        .sorted(Comparator.comparing(SmartScheduleGroomingDetailsDTO::getStartTime))
                        .toList();
                // 在相邻 petService 之间，构建 TimeSlot
                List<TimeSlot> slotBetweenPetService = buildFreeTimeSlotsByStaffId(
                        curWorkingTime.get(0).getStartTime(),
                        curWorkingTime.get(curWorkingTime.size() - 1).getEndTime(),
                        petDetailInfoList);
                // 填充空闲 timeSlot 前后预约的地址信息
                StaffSmartScheduleSettingDTO staffSmartScheduleSettingDTO = null;
                if (ssParams.getIsSmartScheduleEnable()) {
                    staffSmartScheduleSettingDTO =
                            ssParams.getStaffSsSettingMap().get(staffId);
                }
                fillServiceAddressV2(
                        slotBetweenPetService,
                        staffSmartScheduleSettingDTO,
                        ssParams.getExistAppointmentCustomerAddress());
                // 过滤非工作时间
                List<TimeSlot> freeSlot = filterNonWorkingTime(slotBetweenPetService, curWorkingTime);

                // 时间过滤
                freeSlot = timeFilter(freeSlot, date, params);
                freeSlot =
                        freeSlot.stream().filter(k -> k.getEnd() > k.getStart()).toList();
                if (!CollectionUtils.isEmpty(freeSlot)) {
                    result.computeIfAbsent(dateStr, k -> new HashMap<>()).put(staffId, freeSlot);
                }
            }
        }
        return result;
    }

    List<TimeSlot> getAvailableTimeSlot(BatchSSParams ssParams, GetAvailableSlotParams params) {
        if (CollectionUtils.isEmpty(params.getTimeSlots())) {
            return List.of();
        }
        int bufferTime = ssParams.getIsSmartScheduleEnable() ? SMART_SCHEDULE_BUFFER_TIME : 0;
        List<TimeSlot> step1Times = params.getTimeSlots().stream()
                .filter(t -> t.getEnd() - t.getStart() >= params.getServiceDuration() + bufferTime * 2)
                .toList();

        if (!ssParams.getIsSmartScheduleEnable()) {
            if (params.getNeedCount() != COUNT_FOR_AVAILABLE_TIME_SLOT_NEED) {
                return step1Times.stream().limit(params.getNeedCount()).toList();
            }
            return step1Times;
        }

        List<TimeSlot> resultList = new ArrayList<>();
        LocationParams location = ssParams.getLocations().get(params.getLocationId());
        if (location == null) {
            return resultList;
        }
        if (params.getNeedCount() == COUNT_FOR_AVAILABLE_TIME_SLOT_NEED) {
            // 对所有 timeslot 查询驾驶时间，返回满足条件的所有 timeslot
            params.getDriverTimeCalculator()
                    .apply(
                            step1Times,
                            location.getLat().toString(),
                            location.getLng().toString(),
                            params.getServiceDuration() + bufferTime);
            drivenInfoAdjustment(step1Times, params.getServiceDuration(), params.getDistanceScaleFactor());
            resultList = step1Times.stream()
                    // 剩余时间足够
                    .filter(t -> (0 <= t.getAvailableTime()))
                    // 开车时间合适
                    .filter(t -> smartScheduleService.meetMaxDrivingTimeAndDist(t, params.getDrivingRule()))
                    .collect(Collectors.toList());
        } else {
            // 以单个timeslot去查询驾驶时间，满足条件则返回，不再往下查找
            for (TimeSlot slot : step1Times) {
                if (resultList.size() >= params.getNeedCount()) {
                    break;
                }
                params.getDriverTimeCalculator()
                        .apply(
                                Collections.singletonList(slot),
                                location.getLat().toString(),
                                location.getLng().toString(),
                                params.getServiceDuration() + bufferTime);
                drivenInfoAdjustment(
                        Collections.singletonList(slot), params.getServiceDuration(), params.getDistanceScaleFactor());
                if (slot.getAvailableTime() >= 0
                        && smartScheduleService.meetMaxDrivingTimeAndDist(slot, params.getDrivingRule())) {
                    resultList.add(slot);
                }
            }
        }
        smartScheduleService.updateTimeWithDrivingTime(resultList);
        smartScheduleService.checkFirstOrLastAppt(resultList);
        return resultList;
    }

    /**
     *
     * @return 返回两个元素，第一个元素表示是否支持CACD，第二个元素表示是否支持SS
     */
    List<Boolean> getCACDAndSSFlag(boolean isOB, Integer businessId) {
        List<Boolean> result = new ArrayList<>();
        OBBusinessInfoDTO obBusinessInfoDTO =
                iBusinessBusinessClient.getBusinessInfoForOB(new InfoIdParams(businessId));
        if (isOB) {
            MoeBusinessBookOnline businessBookOnline = businessService.getSettingInfoByBusinessId(businessId);
            // Mobile / Hybrid
            boolean notSalon =
                    !Objects.equals(obBusinessInfoDTO.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_SALON);
            boolean needAddress = Objects.equals(businessBookOnline.getIsNeedAddress(), CommonConstant.ENABLE);

            boolean enableCACD = Objects.equals(businessBookOnline.getServiceAreaEnable(), CommonConstant.ENABLE);
            result.add(notSalon && needAddress && enableCACD);

            boolean enableSS = Objects.equals(businessBookOnline.getSmartScheduleEnable(), CommonConstant.ENABLE);
            result.add(notSalon && needAddress && enableSS);
        } else {
            var serviceAreaEnable = smartScheduleSettingServiceBlockingStub
                    .getSmartScheduleSetting(GetSmartScheduleSettingRequest.newBuilder()
                            .setTokenCompanyId(obBusinessInfoDTO.getCompanyId().longValue())
                            .build())
                    .getSmartScheduleSetting()
                    .getServiceAreaEnable();
            result.add(serviceAreaEnable);

            result.add(!Objects.equals(obBusinessInfoDTO.getBusinessMode(), SubscriptionConst.BUSINESS_TYPE_SALON));
        }
        return result;
    }

    Map<String, Map<Integer, List<TimeRangeDto>>> getStaffPerDayWorkingTime(
            Boolean isOB, Integer businessId, List<Integer> staffIdList, LocalDate startDate, LocalDate endDate) {
        Map<String, Map<Integer, List<TimeRangeDto>>> result = new HashMap<>();
        if (isOB) {
            // to complete
            return null;
        } else {
            Map<Integer, Map<String, List<TimeRangeDto>>> staffWorkingRange =
                    iBusinessStaffClient.queryStaffWorkingHourRange(
                            businessId, staffIdList, startDate.toString(), endDate.toString());
            for (Map.Entry<Integer, Map<String, List<TimeRangeDto>>> entry : staffWorkingRange.entrySet()) {
                Integer staffId = entry.getKey();
                for (Map.Entry<String, List<TimeRangeDto>> entry1 :
                        entry.getValue().entrySet()) {
                    String date = entry1.getKey();
                    result.computeIfAbsent(date, k -> new HashMap<>()).put(staffId, entry1.getValue());
                }
            }
        }
        return result;
    }

    Map<String, Map<Integer, List<TimeRangeDto>>> cacdResult2StaffWorkingTime(
            Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> cacdResult) {
        Map<String, Map<Integer, List<TimeRangeDto>>> result = new HashMap<>();
        for (Map.Entry<String, Map<Integer, List<WorkingTimeAndAreaDto>>> entry : cacdResult.entrySet()) {
            Map<Integer, List<TimeRangeDto>> staffTimeRangeMap = new HashMap<>();
            for (Map.Entry<Integer, List<WorkingTimeAndAreaDto>> entry1 :
                    entry.getValue().entrySet()) {
                List<TimeRangeDto> timeRangeDtoList = entry1.getValue().stream()
                        .map(workingTimeAndAreaDto -> new TimeRangeDto(
                                workingTimeAndAreaDto.getStartTime(), workingTimeAndAreaDto.getEndTime()))
                        .collect(Collectors.toList());
                staffTimeRangeMap.put(entry1.getKey(), timeRangeDtoList);
            }
            result.put(entry.getKey(), staffTimeRangeMap);
        }
        return result;
    }

    List<TimeSlot> timeFilter(List<TimeSlot> timeSlots, LocalDate curDate, GetFreeSlotParams params) {
        timeSlots = boundaryTimeFilter(timeSlots, curDate, params.getStartDateTime(), params.getEndDateTime());
        timeSlots = filterExactStartTime(params.getExpectStartTimeList(), timeSlots);
        timeSlots = timeSlotListIntersection(timeSlots, params.getExpectTimeRangeList());
        return timeSlots;
    }

    public List<TimeSlot> boundaryTimeFilter(
            List<TimeSlot> timeSlots, LocalDate curDate, LocalDateTime startTime, LocalDateTime entTime) {
        if (curDate.isEqual(startTime.toLocalDate())) {
            timeSlots =
                    filterStartTime(DateUtil.getMinsByHourMins(startTime.getHour(), startTime.getMinute()), timeSlots);
        }
        if (curDate.isEqual(entTime.toLocalDate())) {
            timeSlots = filterEndTime(DateUtil.getMinsByHourMins(entTime.getHour(), entTime.getMinute()), timeSlots);
        }
        return timeSlots;
    }

    /**
     * @param exactStartTimeList 多个 startTime 满足一个即可。startTimeMinutes 按从小到大排序
     */
    List<TimeSlot> filterExactStartTime(List<Integer> exactStartTimeList, List<TimeSlot> timeSlots) {
        if (CollectionUtils.isEmpty(exactStartTimeList)) {
            return timeSlots;
        }
        List<TimeSlot> result = new ArrayList<>();
        for (TimeSlot timeSlot : timeSlots) {
            for (Integer startTimeMinute : exactStartTimeList) {
                if (timeSlot.getStart() <= startTimeMinute && timeSlot.getEnd() > startTimeMinute) {
                    timeSlot.setStart(startTimeMinute);
                    result.add(timeSlot);
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 过滤出起始时间大于 startTime 的 timeSlot
     */
    public List<TimeSlot> filterStartTime(Integer startTime, List<TimeSlot> timeSlots) {
        List<TimeSlot> result = new ArrayList<>();
        for (TimeSlot timeSlot : timeSlots) {
            if (startTime >= timeSlot.getEnd()) {
                continue;
            }
            if (startTime > timeSlot.getStart()) {
                timeSlot.setStart(startTime);
            }
            result.add(timeSlot);
        }

        return result;
    }

    /**
     * 过滤出结束时间小于 endTime 的 timeSlot
     */
    public List<TimeSlot> filterEndTime(Integer endTime, List<TimeSlot> timeSlots) {
        List<TimeSlot> result = new ArrayList<>();
        for (TimeSlot timeSlot : timeSlots) {
            if (endTime <= timeSlot.getStart()) {
                continue;
            }
            if (endTime < timeSlot.getEnd()) {
                timeSlot.setEnd(endTime);
            }
            result.add(timeSlot);
        }

        return result;
    }

    boolean dateCheck(LocalDate targetDate, List<Integer> preferredDayOfWeek, List<LocalDate> preferredDate) {
        if (preferredDate != null) {
            if (!preferredDate.contains(targetDate)) {
                return false;
            }
        }
        if (preferredDayOfWeek != null) {
            return preferredDayOfWeek.contains(getDayOfWeek(targetDate));
        }
        return true;
    }

    /**
     * 针对一些场景，对计算的驾驶时间、驾驶距离进行调整。目前用在 waitList 场景，用于减小直线预估两点间距离的误差
     */
    void drivenInfoAdjustment(List<TimeSlot> timeSlots, int serviceDuration, Double distanceScaleFactor) {
        if (distanceScaleFactor == null) {
            return;
        }
        for (TimeSlot slot : timeSlots) {
            slot.setDriveInMiles(BigDecimal.valueOf(slot.getDriveInMiles() * distanceScaleFactor)
                    .setScale(1, RoundingMode.FLOOR)
                    .doubleValue());
            slot.setDriveInMinutes((int) (slot.getDriveInMinutes() * distanceScaleFactor));
            slot.setDriveOutMiles(BigDecimal.valueOf(slot.getDriveOutMiles() * distanceScaleFactor)
                    .setScale(1, RoundingMode.FLOOR)
                    .doubleValue());
            slot.setDriveOutMinutes((int) (slot.getDriveOutMinutes() * distanceScaleFactor));
            slot.updateAvailableTime(serviceDuration + slot.getDriveInMinutes() + slot.getDriveOutMinutes());
        }
    }

    /**
     * 根据职工的预约信息，构建职工的空闲时间段。
     * @param workStartTime 当天工作开始时间
     * @param workEndTime 当天工作结束时间
     * @param petDetailInfoList 当天预约信息
     * @return 职工的空闲时间段
     */
    public List<TimeSlot> buildFreeTimeSlotsByStaffId(
            Integer workStartTime, Integer workEndTime, List<SmartScheduleGroomingDetailsDTO> petDetailInfoList) {
        List<TimeSlot> result = new ArrayList<>();
        SmartScheduleGroomingDetailsDTO preService = new SmartScheduleGroomingDetailsDTO();
        preService.setEndTime(workStartTime.longValue());
        preService.setGroomingId(-1);
        preService.setServiceId(-1);
        preService.setCustomerId(-1);

        for (SmartScheduleGroomingDetailsDTO curService : petDetailInfoList) {
            if (preService.getEndTime() <= curService.getStartTime()) {
                if (preService.getEndTime() >= workEndTime || curService.getStartTime() > workEndTime) {
                    break;
                }
                TimeSlot slot = TimeSlot.builder()
                        .start(Math.toIntExact(preService.getEndTime()))
                        .end(Math.toIntExact(curService.getStartTime()))
                        .beforeApptId(preService.getGroomingId())
                        .beforeServiceId(preService.getServiceId())
                        .beforeCustomerId(preService.getCustomerId())
                        .afterApptId(curService.getGroomingId())
                        .afterServiceId(curService.getServiceId())
                        .afterCustomerId(curService.getCustomerId())
                        .build();
                result.add(slot);
                preService = curService;
            } else if (preService.getEndTime() < curService.getEndTime()) {
                preService = curService;
            }
        }

        if (preService.getEndTime() < workEndTime) {
            result.add(TimeSlot.builder()
                    .start(Math.toIntExact(preService.getEndTime()))
                    .end(workEndTime)
                    .beforeApptId(preService.getGroomingId())
                    .beforeServiceId(preService.getServiceId())
                    .beforeCustomerId(preService.getCustomerId())
                    .afterApptId(-1)
                    .afterServiceId(-1)
                    .build());
        }
        return result;
    }
}
