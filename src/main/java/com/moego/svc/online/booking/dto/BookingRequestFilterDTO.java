package com.moego.svc.online.booking.dto;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationRequest;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/4/11
 */
@Data
@Accessors(chain = true)
public class BookingRequestFilterDTO {
    private List<Long> businessIds;
    private List<Integer> statuses;
    private String startDate;
    private String endDate;
    private String latestEndDate;
    private PaginationRequest pagination;
    private List<OrderBy> orderBys;
    private List<ServiceItemType> serviceItems;
    private List<Integer> serviceTypeIncludes;
    private Long companyId;
    private Collection<Long> customerIds;
    private List<Long> appointmentIds;
    private Date createdBefore;
    private Date createdAfter;
    private List<BookingRequestModel.PaymentStatus> paymentStatuses;
    private List<BookingRequestModel.Source> sources;
    private Boolean isWaitlistExpired;
    private LocalDate expiredDate;
    private String keyword;
}
