package com.moego.svc.online.booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.FAILED;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.NO_PAYMENT;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.PAYMENT_STATUS_UNSPECIFIED;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.PROCESSING;
import static com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus.SUCCESS;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.svc.online.booking.mapper.BoardingAddOnDetailDynamicSqlSupport.boardingAddOnDetail;
import static com.moego.svc.online.booking.mapper.BoardingAutoAssignDynamicSqlSupport.boardingAutoAssign;
import static com.moego.svc.online.booking.mapper.BoardingServiceDetailDynamicSqlSupport.boardingServiceDetail;
import static com.moego.svc.online.booking.mapper.BookingRequestDynamicSqlSupport.bookingRequest;
import static com.moego.svc.online.booking.mapper.BookingRequestMapper.updateSelectiveColumns;
import static com.moego.svc.online.booking.mapper.DaycareAddOnDetailDynamicSqlSupport.daycareAddOnDetail;
import static com.moego.svc.online.booking.mapper.DaycareServiceDetailDynamicSqlSupport.daycareServiceDetail;
import static com.moego.svc.online.booking.mapper.FeedingDynamicSqlSupport.feeding;
import static com.moego.svc.online.booking.mapper.GroomingAddOnDetailDynamicSqlSupport.groomingAddOnDetail;
import static com.moego.svc.online.booking.mapper.GroomingAutoAssignDynamicSqlSupport.groomingAutoAssign;
import static com.moego.svc.online.booking.mapper.GroomingServiceDetailDynamicSqlSupport.groomingServiceDetail;
import static com.moego.svc.online.booking.mapper.MedicationDynamicSqlSupport.medication;
import static java.util.stream.Collectors.groupingBy;
import static org.mybatis.dynamic.sql.SqlBuilder.and;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isGreaterThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLessThanOrEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;
import static org.springframework.util.ObjectUtils.isEmpty;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.PageUtil;
import com.moego.common.utils.Pagination;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.DeleteAppointmentsRequest;
import com.moego.idl.service.appointment.v1.RestoreAppointmentsRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingRequestRequest;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.svc.online.booking.dto.BookingRequestFilterDTO;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.GroomingAddOnDetail;
import com.moego.svc.online.booking.entity.GroomingAutoAssign;
import com.moego.svc.online.booking.entity.GroomingServiceDetail;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.helper.CustomerHelper;
import com.moego.svc.online.booking.helper.ServiceHelper;
import com.moego.svc.online.booking.mapper.BoardingAddOnDetailMapper;
import com.moego.svc.online.booking.mapper.BoardingAutoAssignMapper;
import com.moego.svc.online.booking.mapper.BoardingServiceDetailMapper;
import com.moego.svc.online.booking.mapper.BookingRequestMapper;
import com.moego.svc.online.booking.mapper.DaycareAddOnDetailMapper;
import com.moego.svc.online.booking.mapper.DaycareServiceDetailMapper;
import com.moego.svc.online.booking.mapper.FeedingMapper;
import com.moego.svc.online.booking.mapper.GroomingAddOnDetailMapper;
import com.moego.svc.online.booking.mapper.GroomingAutoAssignMapper;
import com.moego.svc.online.booking.mapper.GroomingServiceDetailMapper;
import com.moego.svc.online.booking.mapper.MedicationMapper;
import com.moego.svc.online.booking.mapstruct.BoardingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.BoardingAutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.BoardingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.BoardingServiceWaitlistConverter;
import com.moego.svc.online.booking.mapstruct.BookingRequestConverter;
import com.moego.svc.online.booking.mapstruct.DaycareAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.DaycareServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.DaycareServiceWaitlistConverter;
import com.moego.svc.online.booking.mapstruct.DogWalkingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.EvaluationTestDetailConverter;
import com.moego.svc.online.booking.mapstruct.FeedingConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAddOnDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroomingAutoAssignConverter;
import com.moego.svc.online.booking.mapstruct.GroomingServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.GroupClassServiceDetailConverter;
import com.moego.svc.online.booking.mapstruct.MedicationConverter;
import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.AndOrCriteriaGroup;
import org.mybatis.dynamic.sql.SortSpecification;
import org.mybatis.dynamic.sql.SqlColumn;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BookingRequestService {

    private final BookingRequestMapper bookingRequestMapper;
    private final GroomingServiceDetailMapper groomingServiceDetailMapper;
    private final GroomingAddOnDetailMapper groomingAddOnDetailMapper;
    private final GroomingAutoAssignMapper groomingAutoAssignMapper;
    private final BoardingServiceDetailMapper boardingServiceDetailMapper;
    private final BoardingAddOnDetailMapper boardingAddOnDetailMapper;
    private final BoardingAutoAssignMapper boardingAutoAssignMapper;
    private final DaycareServiceDetailMapper daycareServiceDetailMapper;
    private final DaycareAddOnDetailMapper daycareAddOnDetailMapper;
    private final FeedingMapper feedingMapper;
    private final MedicationMapper medicationMapper;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final GroomingServiceDetailService groomingServiceDetailService;
    private final GroomingAddOnDetailService groomingAddOnDetailService;
    private final GroomingAutoAssignService groomingAutoAssignService;
    private final BoardingServiceDetailService boardingServiceDetailService;
    private final BoardingAddOnDetailService boardingAddOnDetailService;
    private final BoardingAutoAssignService boardingAutoAssignService;
    private final DaycareServiceDetailService daycareServiceDetailService;
    private final DaycareAddOnDetailService daycareAddOnDetailService;
    private final EvaluationTestDetailService evaluationTestDetailService;
    private final DogWalkingServiceDetailService dogWalkingServiceDetailService;
    private final GroupClassServiceDetailService groupClassServiceDetailService;
    private final FeedingService feedingService;
    private final MedicationService medicationService;
    private final WaitlistService waitlistService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;

    private static final Map<String, SqlColumn<?>> SORT_COLUMNS = Map.of(
            "startDate", bookingRequest.startDate,
            "startTime", bookingRequest.startTime,
            "createdAt", bookingRequest.createdAt);
    private final BusinessHelper businessHelper;
    private final CustomerHelper customerHelper;
    private final ICustomerCustomerClient customerApi;

    /**
     * Get existed record by id, not include deleted record.
     *
     * @param id id
     * @return existed record or null
     */
    @Nullable
    public BookingRequest get(long id) {
        return bookingRequestMapper
                .selectByPrimaryKey(id)
                .filter(e -> e.getDeletedAt() == null)
                .orElse(null);
    }

    /**
     * Get existed record by id, not include deleted record, throw exception if not found.
     *
     * @param id booking request id
     * @return existed record
     */
    public BookingRequest mustGet(long id) {
        return Optional.ofNullable(get(id))
                .orElseThrow(() -> bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + id));
    }

    /**
     * Insert a record, null properties will be ignored.
     *
     * @param entity entity
     * @return inserted id
     */
    public long insert(BookingRequest entity) {
        check(entity);

        if (entity.getPaymentStatus() == null || entity.getPaymentStatus() == PAYMENT_STATUS_UNSPECIFIED) {
            entity.setPaymentStatus(NO_PAYMENT);
        }

        if (entity.getStatus() == BookingRequestStatus.WAIT_LIST) {
            entity.setPaymentStatus(NO_PAYMENT);
        }

        bookingRequestMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * Delete a record by id.
     *
     * <p> NOTE：会删除改 booking request 关联的所有资源，执行时间会较长，可以考虑异步删除。
     *
     * @param id id
     * @return deleted rows
     */
    @Transactional
    public int delete(long id) {
        int result = bookingRequestMapper.update(c -> c.set(bookingRequest.deletedAt)
                .equalTo(new Date())
                .where(bookingRequest.id, isEqualTo(id))
                .and(bookingRequest.deletedAt, isNull()));

        if (result > 0) {
            deleteRelatedResources(id);
        }

        return result;
    }

    /**
     * Create booking request with grooming services and auto assign record
     *
     * @param bookingRequest booking request
     * @param petServiceMap  pet - grooming service
     * @param petAddOnsMap   pet - grooming add ons
     * @param autoAssign     auto assign staff or time record
     */
    @Transactional
    public void createGroomingOnlyBookingRequest(
            BookingRequest bookingRequest,
            Map<Integer, GroomingServiceDetail> petServiceMap,
            Map<Integer, List<GroomingAddOnDetail>> petAddOnsMap,
            GroomingAutoAssign autoAssign) {
        bookingRequest.setSource(BookingRequestModel.Source.OB);
        insert(bookingRequest);
        createServiceAddOn(bookingRequest, petServiceMap, petAddOnsMap, autoAssign);
    }

    @Transactional
    public void updateGroomingOnlyBookingRequest(
            BookingRequest bookingRequest,
            Map<Integer, GroomingServiceDetail> petServiceMap,
            Map<Integer, List<GroomingAddOnDetail>> petAddOnsMap,
            GroomingAutoAssign autoAssign) {
        bookingRequestMapper.updateByPrimaryKeySelective(bookingRequest);
        // 先删除关联 service 和 add on
        groomingAddOnDetailMapper.delete(
                c -> c.where(groomingAddOnDetail.bookingRequestId, isEqualTo(bookingRequest.getId())));
        groomingServiceDetailMapper.delete(
                c -> c.where(groomingServiceDetail.bookingRequestId, isEqualTo(bookingRequest.getId())));
        groomingAutoAssignMapper.delete(
                c -> c.where(groomingAutoAssign.bookingRequestId, isEqualTo(bookingRequest.getId())));
        // 再关联插入新的 service 和 add on
        createServiceAddOn(bookingRequest, petServiceMap, petAddOnsMap, autoAssign);
    }

    private void createServiceAddOn(
            BookingRequest bookingRequest,
            Map<Integer, GroomingServiceDetail> petServiceMap,
            Map<Integer, List<GroomingAddOnDetail>> petAddOnsMap,
            GroomingAutoAssign autoAssign) {
        petServiceMap.forEach((petId, serviceDetail) -> {
            serviceDetail.setBookingRequestId(bookingRequest.getId());
            groomingServiceDetailMapper.insertSelective(serviceDetail);
            List<GroomingAddOnDetail> addOns = petAddOnsMap.get(petId);
            if (CollectionUtils.isEmpty(addOns)) {
                return;
            }
            addOns.forEach(addOnDetail -> {
                addOnDetail.setBookingRequestId(bookingRequest.getId());
                addOnDetail.setServiceDetailId(serviceDetail.getId());
                groomingAddOnDetailMapper.insertSelective(addOnDetail);
            });
        });
        if (autoAssign != null) {
            autoAssign.setBookingRequestId(bookingRequest.getId());
            groomingAutoAssignMapper.insertSelective(autoAssign);
        }
    }

    public void updateByAppointmentId(BookingRequest request) {
        if (request.getBusinessId() == null || request.getAppointmentId() == null) {
            return;
        }
        bookingRequestMapper.update(c -> updateSelectiveColumns(request, c)
                .where(bookingRequest.appointmentId, isEqualTo(request.getAppointmentId()))
                .and(bookingRequest.businessId, isEqualTo(request.getBusinessId())));
    }

    public BookingRequest getByAppointmentId(Integer businessId, Integer appointmentId) {
        if (businessId == null || appointmentId == null) {
            return null;
        }
        return bookingRequestMapper
                .selectOne(c -> c.where(bookingRequest.appointmentId, isEqualTo(appointmentId.longValue()))
                        .and(bookingRequest.businessId, isEqualTo(businessId.longValue())))
                .orElse(null);
    }

    public int deleteByCustomerId(long businessId, long customerId) {
        BookingRequest update = new BookingRequest();
        update.setDeletedAt(new Date());
        return bookingRequestMapper.update(c -> updateSelectiveColumns(update, c)
                .where(bookingRequest.businessId, isEqualTo(businessId))
                .and(bookingRequest.customerId, isEqualTo(customerId))
                .and(bookingRequest.deletedAt, isNull()));
    }

    public List<BookingRequest> listByCustomerId(long businessId, long customerId) {
        return bookingRequestMapper.select(c -> c.where(bookingRequest.businessId, isEqualTo(businessId))
                .and(bookingRequest.customerId, isEqualTo(customerId))
                .and(bookingRequest.deletedAt, isNull()));
    }

    public Pair<List<BookingRequest>, Pagination> listByBusinessFilter(
            BookingRequestFilterDTO filter, Pagination pagination) {
        if (CollectionUtils.isEmpty(filter.getBusinessIds()) && filter.getCompanyId() == null) {
            return Pair.of(List.of(), pagination);
        }

        if (StringUtils.hasText(filter.getKeyword())) {
            var customerIds = searchCustomerIds(filter);
            if (customerIds.isEmpty()) {
                return Pair.of(List.of(), pagination);
            }
            filter.setCustomerIds(customerIds);
        }

        return PageUtil.selectPage(
                pagination,
                () -> bookingRequestMapper.select(c -> c.where(bookingRequest.deletedAt, isNull())
                        .and(buildCriteria(filter))
                        .orderBy(buildSorts(filter.getOrderBys()))));
    }

    private Set<Long> searchCustomerIds(BookingRequestFilterDTO filter) {
        long companyId;
        if (filter.getCompanyId() != null) {
            companyId = filter.getCompanyId();
        } else {
            if (ObjectUtils.isEmpty(filter.getBusinessIds())) {
                return Set.of();
            }
            companyId = businessHelper.mustGetCompanyId(filter.getBusinessIds().get(0));
        }

        // 逻辑参考 com.moego.api.v3.appointment.controller.OverviewController.listOverviewAppointment
        var result = new HashSet<Long>();
        result.addAll(getCustomerIdsByKeyword(companyId, filter.getKeyword()));
        result.addAll(customerApi.searchCustomerIdsByFullText(companyId, filter.getKeyword()));
        return result;
    }

    private Set<Long> getCustomerIdsByKeyword(long companyId, String keyword) {
        var param = new SearchCustomerIdsParam().setCompanyId(companyId).setKeyword(keyword);
        return customerApi.searchCustomerIds(param).stream()
                .map(Integer::longValue)
                .collect(Collectors.toSet());
    }

    /*private*/
    static List<AndOrCriteriaGroup> buildCriteria(BookingRequestFilterDTO filter) {
        List<AndOrCriteriaGroup> criteria = new ArrayList<>();
        criteria.add(and(bookingRequest.businessId, isInWhenPresent(filter.getBusinessIds())));
        criteria.add(and(bookingRequest.companyId, isEqualToWhenPresent(filter.getCompanyId())));
        criteria.add(and(bookingRequest.customerId, isInWhenPresent(filter.getCustomerIds())));
        criteria.add(and(bookingRequest.appointmentId, isInWhenPresent(filter.getAppointmentIds())));
        if (!CollectionUtils.isEmpty(filter.getStatuses())) {
            criteria.add(and(
                    bookingRequest.status,
                    isInWhenPresent(filter.getStatuses().stream()
                            .map(BookingRequestStatus::forNumber)
                            .toList())));
        }
        if (StringUtils.hasText(filter.getStartDate())) {
            criteria.add(and(bookingRequest.startDate, isGreaterThanOrEqualTo(filter.getStartDate())));
        }
        if (StringUtils.hasText(filter.getEndDate())) {
            criteria.add(and(bookingRequest.startDate, isLessThanOrEqualTo(filter.getEndDate())));
        }
        if (StringUtils.hasText(filter.getLatestEndDate())) {
            criteria.add(and(bookingRequest.endDate, isLessThanOrEqualTo(filter.getLatestEndDate())));
        }
        if (!CollectionUtils.isEmpty(filter.getServiceItems())) {
            criteria.add(and(
                    bookingRequest.serviceTypeInclude,
                    isInWhenPresent(
                            ServiceItemEnum.convertServiceItemListToBitValueList(filter.getServiceItems().stream()
                                    .map(ServiceItemType::getNumber)
                                    .collect(Collectors.toList())))));
        }
        if (!CollectionUtils.isEmpty(filter.getServiceTypeIncludes())) {
            criteria.add(and(bookingRequest.serviceTypeInclude, isInWhenPresent(filter.getServiceTypeIncludes())));
        }
        if (filter.getCreatedBefore() != null) {
            criteria.add(and(bookingRequest.createdAt, isLessThanOrEqualTo(filter.getCreatedBefore())));
        }
        if (filter.getCreatedAfter() != null) {
            criteria.add(and(bookingRequest.createdAt, isGreaterThanOrEqualTo(filter.getCreatedAfter())));
        }
        if (!CollectionUtils.isEmpty(filter.getPaymentStatuses())) {
            criteria.add(and(bookingRequest.paymentStatus, isInWhenPresent(filter.getPaymentStatuses())));
        }
        if (!CollectionUtils.isEmpty(filter.getSources())) {
            criteria.add(and(bookingRequest.source, isIn(filter.getSources())));
        }
        // 获取三个月前的日期
        LocalDate threeMonthsAgo = LocalDate.now().minusMonths(3);
        if (filter.getExpiredDate() != null) {
            threeMonthsAgo = filter.getExpiredDate();
        }
        if (filter.getIsWaitlistExpired() != null && filter.getIsWaitlistExpired()) {
            criteria.add(and(bookingRequest.startDate, isLessThanOrEqualTo(threeMonthsAgo.toString())));
        }
        return criteria;
    }

    private static List<SortSpecification> buildSorts(List<OrderBy> orderBys) {
        List<SortSpecification> sorts = orderBys.stream()
                .map(orderBy -> {
                    SqlColumn<?> sortColumn = SORT_COLUMNS.get(orderBy.getFieldName());
                    if (sortColumn == null) {
                        throw bizException(Code.CODE_PARAMS_ERROR, "Invalid sort field: " + orderBy.getFieldName());
                    }
                    if (orderBy.getAsc()) {
                        return sortColumn;
                    }
                    return sortColumn.descending();
                })
                .collect(Collectors.toList());
        sorts.add(bookingRequest.id.descending());
        return sorts;
    }

    public int updateStatus(long id, BookingRequestStatus status) {
        BookingRequest updateBean = new BookingRequest();
        updateBean.setStatus(status);
        updateBean.setUpdatedAt(new Date());
        return bookingRequestMapper.update(c -> updateSelectiveColumns(updateBean, c)
                .where(bookingRequest.id, isEqualTo(id))
                .and(bookingRequest.deletedAt, isNull())
                .and(bookingRequest.status, isNotEqualTo(status)));
    }

    public int update(BookingRequest request) {
        if (request.getUpdatedAt() == null) {
            request.setUpdatedAt(new java.util.Date());
        }

        var affectedRow = bookingRequestMapper.update(c -> updateSelectiveColumns(request, c)
                .where(bookingRequest.id, isEqualTo(request.getId()))
                .and(bookingRequest.deletedAt, isNull()));

        if (affectedRow > 0) {

            processAppointment(request.getId(), request.getPaymentStatus());
        }

        return affectedRow;
    }

    private void processAppointment(
            long bookingRequestId, @Nullable BookingRequestModel.PaymentStatus updatedPaymentStatus) {

        if (updatedPaymentStatus == null) {
            return;
        }

        var bookingRequest = mustGet(bookingRequestId);

        var appointmentId = bookingRequest.getAppointmentId();
        if (!isNormal(appointmentId)) {
            return;
        }

        if (updatedPaymentStatus == FAILED) {

            // 如果将 request 的 payment status 更新为失败，需要将对应的 appointment 删除掉。
            // 这样做是为了解决：grooming booking request 在 submit 成功 prepay 失败的情况下，mobile 端还能看到 booking request 的问题。
            // BD 的 appointment 和 booking request 已经完全隔离，不会出现这种 case。
            // see https://moego.atlassian.net/browse/MER-3735

            // 只处理 submitted 状态的 booking request，假如是 preauth 支付失败，不能直接删除 appointment
            if (Objects.equals(bookingRequest.getStatus(), BookingRequestStatus.SUBMITTED)) {
                appointmentStub.deleteAppointments(DeleteAppointmentsRequest.newBuilder()
                        .addIds(appointmentId)
                        .build());
            }

        } else if (updatedPaymentStatus == PROCESSING || updatedPaymentStatus == SUCCESS) {

            // 这里还需要兼容支付失败，然后换卡支付成功的场景，这时候需要把删除的 appointment 恢复
            // see
            // https://moegoworkspace.slack.com/archives/C01TT9K995M/p1742282058600329?thread_ts=1741855116.300369&cid=C01TT9K995M

            appointmentStub.restoreAppointments(RestoreAppointmentsRequest.newBuilder()
                    .addIds(appointmentId)
                    .build());
        }
    }

    private void deleteRelatedResources(long bookingRequestId) {
        groomingServiceDetailMapper.update(c -> c.set(groomingServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(groomingServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(groomingServiceDetail.deletedAt, isNull()));
        groomingAddOnDetailMapper.update(c -> c.set(groomingAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(groomingAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(groomingAddOnDetail.deletedAt, isNull()));
        groomingAutoAssignMapper.update(c -> c.set(groomingAutoAssign.deletedAt)
                .equalTo(new Date())
                .where(groomingAutoAssign.bookingRequestId, isEqualTo(bookingRequestId))
                .and(groomingAutoAssign.deletedAt, isNull()));
        boardingServiceDetailMapper.update(c -> c.set(boardingServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(boardingServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(boardingServiceDetail.deletedAt, isNull()));
        boardingAddOnDetailMapper.update(c -> c.set(boardingAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(boardingAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(boardingAddOnDetail.deletedAt, isNull()));
        boardingAutoAssignMapper.update(c -> c.set(boardingAutoAssign.deletedAt)
                .equalTo(new Date())
                .where(boardingAutoAssign.bookingRequestId, isEqualTo(bookingRequestId))
                .and(boardingAutoAssign.deletedAt, isNull()));
        daycareServiceDetailMapper.update(c -> c.set(daycareServiceDetail.deletedAt)
                .equalTo(new Date())
                .where(daycareServiceDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(daycareServiceDetail.deletedAt, isNull()));
        daycareAddOnDetailMapper.update(c -> c.set(daycareAddOnDetail.deletedAt)
                .equalTo(new Date())
                .where(daycareAddOnDetail.bookingRequestId, isEqualTo(bookingRequestId))
                .and(daycareAddOnDetail.deletedAt, isNull()));
        feedingMapper.update(c -> c.set(feeding.deletedAt)
                .equalTo(new Date())
                .where(feeding.bookingRequestId, isEqualTo(bookingRequestId))
                .and(feeding.deletedAt, isNull()));
        medicationMapper.update(c -> c.set(medication.deletedAt)
                .equalTo(new Date())
                .where(medication.bookingRequestId, isEqualTo(bookingRequestId))
                .and(medication.deletedAt, isNull()));
    }

    private static void check(BookingRequest entity) {
        if (!isNormal(entity.getCompanyId())) throw bizException(Code.CODE_PARAMS_ERROR, "companyId is required");
        if (!isNormal(entity.getBusinessId())) throw bizException(Code.CODE_PARAMS_ERROR, "businessId is required");
        if (!isNormal(entity.getCustomerId())) throw bizException(Code.CODE_PARAMS_ERROR, "customerId is required");
        if (!isNormal(entity.getServiceTypeInclude()))
            throw bizException(Code.CODE_PARAMS_ERROR, "serviceTypeInclude is required");
    }

    public int countBookingRequest(BookingRequestFilterDTO filter) {
        return Math.toIntExact(bookingRequestMapper.count(
                c -> c.where(bookingRequest.deletedAt, isNull()).and(buildCriteria(filter))));
    }

    /**
     * Get booking request model by id.
     *
     * @param bookingRequestId the booking request id
     * @param models           associated models to include
     * @return BookingRequestModel or null if not found
     */
    @Nullable
    public BookingRequestModel getBookingRequestModel(GetBookingRequestRequest request) {
        return listBookingRequestModel(
                        request.hasCompanyId() ? request.getCompanyId() : null,
                        request.hasBusinessId() ? request.getBusinessId() : null,
                        List.of(request.getId()),
                        Set.copyOf(request.getAssociatedModelsList()))
                .get(request.getId());
    }

    /**
     * Get booking request model by id, throw exception if not found.
     *
     * @param bookingRequestId booking request id
     * @param models           associated models to include
     * @return BookingRequestModel or throw exception if not found
     */
    public BookingRequestModel mustGetBookingRequestModel(GetBookingRequestRequest request) {
        return Optional.ofNullable(getBookingRequestModel(request))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "Booking request not found: " + request.getId()));
    }

    /**
     * List booking request models by ids.
     *
     * @param bookingRequestIds booking request ids
     * @param models            associated models to include
     * @return booking request models
     */
    private Map<Long, BookingRequestModel> listBookingRequestModel(
            @Nullable Long companyId,
            @Nullable Long businessId,
            List<Long> bookingRequestIds,
            Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequestIds)) {
            return Map.of();
        }

        var bookingRequests = listBookingRequestByIds(companyId, businessId, bookingRequestIds);
        var bookingRequestIdToServiceDetails = listServiceDetail(bookingRequests, models);

        return bookingRequests.stream()
                .map(request -> {
                    var serviceDetails = bookingRequestIdToServiceDetails.getOrDefault(request.getId(), List.of());
                    return buildBookingRequestModel(request, serviceDetails);
                })
                .collect(Collectors.toMap(BookingRequestModel::getId, Function.identity(), (o, n) -> o));
    }

    private static BookingRequestModel buildBookingRequestModel(
            BookingRequest bookingRequest, List<BookingRequestModel.Service> services) {
        var modelBuilder = BookingRequestConverter.INSTANCE.entityToModel(bookingRequest).toBuilder();
        modelBuilder.addAllServices(services);
        return modelBuilder.build();
    }

    private List<BookingRequest> listBookingRequestByIds(
            @Nullable Long companyId, @Nullable Long businessId, List<Long> bookingRequestIds) {
        if (bookingRequestIds.isEmpty()) {
            return List.of();
        }
        return bookingRequestMapper.select(c -> c.where(bookingRequest.id, isIn(bookingRequestIds))
                .and(bookingRequest.companyId, isEqualToWhenPresent(companyId))
                .and(bookingRequest.businessId, isEqualToWhenPresent(businessId))
                .and(bookingRequest.deletedAt, isNull()));
    }

    /**
     * List services by booking requests.
     *
     * <p> 这个方法不应该存在，这里为了收敛 booking request model 的查询逻辑和兼容老代码才暴露了这个方法。
     *
     * @param bookingRequests booking requests
     * @param models          associated models
     * @return bookingRequestId -> list of services
     */
    public Map<Long, List<BookingRequestModel.Service>> listServiceDetail(
            List<BookingRequest> bookingRequests, Set<BookingRequestAssociatedModel> models) {
        if (isEmpty(bookingRequests) || isEmpty(models)) {
            return Map.of();
        }
        List<Long> bookingRequestIds =
                bookingRequests.stream().map(BookingRequest::getId).toList();
        List<ServiceItemEnum> serviceItems = ServiceItemEnum.convertBitValues(bookingRequests.stream()
                .map(BookingRequest::getServiceTypeInclude)
                .toList());

        var futures = serviceItems.stream()
                .map(serviceItem -> switch (serviceItem) {
                    case GROOMING -> CompletableFuture.supplyAsync(
                            () -> buildGroomingServices(bookingRequests, models), ThreadPool.getSubmitExecutor());
                    case BOARDING -> CompletableFuture.supplyAsync(
                            () -> buildBoardingServices(bookingRequestIds, models), ThreadPool.getSubmitExecutor());
                    case DAYCARE -> CompletableFuture.supplyAsync(
                            () -> buildDaycareServices(bookingRequestIds, models), ThreadPool.getSubmitExecutor());
                    case EVALUATION -> CompletableFuture.supplyAsync(
                            () -> buildEvaluationServices(bookingRequestIds, models), ThreadPool.getSubmitExecutor());
                    case DOG_WALKING -> CompletableFuture.supplyAsync(
                            () -> buildDogWalkingServices(bookingRequestIds, models), ThreadPool.getSubmitExecutor());
                    case GROUP_CLASS -> CompletableFuture.supplyAsync(
                            () -> buildGroupClassServices(bookingRequestIds, models), ThreadPool.getSubmitExecutor());
                    default -> throw bizException(Code.CODE_PARAMS_ERROR, "Unsupported service type: " + serviceItem);
                })
                .toList();

        CompletableFuture.allOf(futures.toArray(CompletableFuture[]::new)).join();

        return futures.stream()
                .map(CompletableFuture::join)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                Map.Entry::getValue, Collectors.flatMapping(List::stream, Collectors.toList()))));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildGroomingServices(
            List<BookingRequest> bookingRequests, Set<BookingRequestAssociatedModel> models) {
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return Map.of();
        }

        List<Long> bookingRequestIds =
                bookingRequests.stream().map(BookingRequest::getId).toList();
        Map<Long, Map<Long, BookingRequestModel.GroomingService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var serviceList = groomingServiceDetailService.listByBookingRequestId(bookingRequestIds);

            var customizedServiceList = listCustomizedService(serviceList, bookingRequests);

            for (var groomingServiceDetail : serviceList) {
                var serviceDetail = GroomingServiceDetailConverter.INSTANCE.entityToModel(groomingServiceDetail);
                var overrideServiceDetail = buildOverrideServiceDetail(serviceDetail, customizedServiceList);
                builderMap
                        .computeIfAbsent(overrideServiceDetail.getBookingRequestId(), id -> new HashMap<>())
                        .computeIfAbsent(
                                overrideServiceDetail.getId(), id -> BookingRequestModel.GroomingService.newBuilder())
                        .setService(overrideServiceDetail);
            }
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            groomingAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.GroomingService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(GroomingAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.AUTO_ASSIGN)) {
            groomingAutoAssignService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, autoAssign) -> {
                        var serviceBuilder = builderMap.get(bookingRequestId);
                        if (!CollectionUtils.isEmpty(serviceBuilder)) {
                            serviceBuilder.values().stream()
                                    .findFirst()
                                    .ifPresent(builder -> builder.setAutoAssign(
                                            GroomingAutoAssignConverter.INSTANCE.entityToModel(autoAssign)));
                        } else {
                            builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            autoAssign.getId(), id -> BookingRequestModel.GroomingService.newBuilder())
                                    .setAutoAssign(GroomingAutoAssignConverter.INSTANCE.entityToModel(autoAssign));
                        }
                    });
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.GroomingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setGrooming(service)
                                .build())
                        .toList()));
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> listCustomizedService(
            List<GroomingServiceDetail> serviceList, List<BookingRequest> bookingRequests) {

        var bookingRequestIdToBookingRequest =
                bookingRequests.stream().collect(Collectors.toMap(BookingRequest::getId, e -> e, (o, n) -> o));

        var companyIdToBookingRequestIdToServiceList = serviceList.stream()
                .collect(groupingBy(
                        service -> bookingRequestIdToBookingRequest
                                .get(service.getBookingRequestId())
                                .getCompanyId(),
                        groupingBy(GroomingServiceDetail::getBookingRequestId)));

        return companyIdToBookingRequestIdToServiceList.entrySet().stream()
                .map(entry -> {
                    var companyId = entry.getKey();
                    var bookingRequestIdToServiceList = entry.getValue();

                    var builder = BatchGetCustomizedServiceRequest.newBuilder().setCompanyId(companyId);

                    bookingRequestIdToServiceList.forEach((bookingRequestId, ServiceDetailList) -> {
                        var businessId = bookingRequestIdToBookingRequest
                                .get(bookingRequestId)
                                .getBusinessId();
                        var conditions = buildCustomizedServiceQueryConditionList(businessId, ServiceDetailList);
                        conditions.forEach(builder::addQueryConditionList);
                    });

                    return serviceStub
                            .batchGetCustomizedService(builder.build())
                            .getCustomizedServiceListList();
                })
                .flatMap(List::stream)
                .toList();
    }

    private static GroomingServiceDetailModel buildOverrideServiceDetail(
            GroomingServiceDetailModel serviceDetail,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList) {

        var builder = serviceDetail.toBuilder();

        var customizedService = ServiceHelper.findCustomizedService(
                customizedServiceList,
                serviceDetail.getServiceId(),
                serviceDetail.getPetId(),
                serviceDetail.getStaffId());
        if (customizedService != null) {
            builder.setPriceOverrideType(customizedService.getPriceOverrideType());
            builder.setDurationOverrideType(customizedService.getDurationOverrideType());
        }

        return builder.build();
    }

    private static List<CustomizedServiceQueryCondition> buildCustomizedServiceQueryConditionList(
            long businessId, List<GroomingServiceDetail> groomingServiceDetailList) {

        var condList = new ArrayList<CustomizedServiceQueryCondition>();
        for (var groomingServiceDetail : groomingServiceDetailList) {
            var cb = CustomizedServiceQueryCondition.newBuilder();
            cb.setServiceId(groomingServiceDetail.getServiceId());
            cb.setBusinessId(businessId);
            if (isNormal(groomingServiceDetail.getPetId())) {
                cb.setPetId(groomingServiceDetail.getPetId());
            }
            if (isNormal(groomingServiceDetail.getStaffId())) {
                cb.setStaffId(groomingServiceDetail.getStaffId());
            }
            condList.add(cb.build());
        }

        return condList;
    }

    private Map<Long, List<BookingRequestModel.Service>> buildBoardingServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.BoardingService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var boardingWaitlistByServiceIdId =
                    waitlistService.boardingMapByServiceDetailIdWithBookingRquestId(bookingRequestIds);
            boardingServiceDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(BoardingServiceDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> {
                        var builder = builderMap
                                .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        serviceDetail.getId(), id -> BookingRequestModel.BoardingService.newBuilder());
                        builder.setService(serviceDetail);
                        if (boardingWaitlistByServiceIdId.containsKey(serviceDetail.getId())) {
                            builder.setWaitlist(BoardingServiceWaitlistConverter.INSTANCE.entityToModel(
                                    boardingWaitlistByServiceIdId.get(serviceDetail.getId())));
                        }
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            boardingAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.BoardingService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(BoardingAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.FEEDING)) {
            feedingService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.BOARDING_VALUE)
                    .forEach((bookingRequestId, bookingRequestFeedings) ->
                            bookingRequestFeedings.forEach((serviceDetailId, feedings) -> {
                                var detail = builderMap
                                        .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                        .computeIfAbsent(
                                                serviceDetailId,
                                                id -> BookingRequestModel.BoardingService.newBuilder());
                                detail.addAllFeedings(FeedingConverter.INSTANCE.entityToModel(feedings));
                                if (!CollectionUtils.isEmpty(feedings)) {
                                    detail.setFeeding(FeedingConverter.INSTANCE.entityToModel(feedings.get(0)));
                                }
                            }));
        }
        if (models.contains(BookingRequestAssociatedModel.MEDICATION)) {
            medicationService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.BOARDING_VALUE)
                    .forEach((bookingRequestId, bookingRequestMedications) -> {
                        bookingRequestMedications.forEach((serviceDetailId, medications) -> {
                            var detail = builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.BoardingService.newBuilder());
                            detail.addAllMedications(MedicationConverter.INSTANCE.entityToModel(medications));
                            if (!CollectionUtils.isEmpty(medications)) {
                                detail.setMedication(MedicationConverter.INSTANCE.entityToModel(medications.get(0)));
                            }
                        });
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.AUTO_ASSIGN)) {
            boardingAutoAssignService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, autoAssign) -> builderMap
                            .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                            .computeIfAbsent(
                                    autoAssign.getBoardingServiceDetailId(),
                                    id -> BookingRequestModel.BoardingService.newBuilder())
                            .setAutoAssign(BoardingAutoAssignConverter.INSTANCE.entityToModel(autoAssign)));
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.BoardingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setBoarding(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildDaycareServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.DaycareService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            var daycareWaitlistByServiceIdId =
                    waitlistService.daycareMapByServiceDetailIdWithBookingRquestId(bookingRequestIds);
            daycareServiceDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(DaycareServiceDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> {
                        var builder = builderMap
                                .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        serviceDetail.getId(), id -> BookingRequestModel.DaycareService.newBuilder());
                        builder.setService(serviceDetail);
                        if (daycareWaitlistByServiceIdId.containsKey(serviceDetail.getId())) {
                            builder.setWaitlist(DaycareServiceWaitlistConverter.INSTANCE.entityToModel(
                                    daycareWaitlistByServiceIdId.get(serviceDetail.getId())));
                        }
                    });
        }
        if (models.contains(BookingRequestAssociatedModel.ADD_ON)) {
            daycareAddOnDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach((bookingRequestId, serviceAddOnsMap) ->
                            serviceAddOnsMap.forEach((serviceDetailId, addOns) -> builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder())
                                    .addAllAddons(addOns.stream()
                                            .map(DaycareAddOnDetailConverter.INSTANCE::entityToModel)
                                            .toList())));
        }
        if (models.contains(BookingRequestAssociatedModel.FEEDING)) {
            feedingService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.DAYCARE_VALUE)
                    .forEach((bookingRequestId, bookingRequestFeedings) ->
                            bookingRequestFeedings.forEach((serviceDetailId, feedings) -> {
                                var detail = builderMap
                                        .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                        .computeIfAbsent(
                                                serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder());
                                detail.addAllFeedings(FeedingConverter.INSTANCE.entityToModel(feedings));
                                if (!CollectionUtils.isEmpty(feedings)) {
                                    detail.setFeeding(FeedingConverter.INSTANCE.entityToModel(feedings.get(0)));
                                }
                            }));
        }
        if (models.contains(BookingRequestAssociatedModel.MEDICATION)) {
            medicationService
                    .listByBookingRequestId(bookingRequestIds, ServiceItemType.DAYCARE_VALUE)
                    .forEach((bookingRequestId, bookingRequestMedications) -> {
                        bookingRequestMedications.forEach((serviceDetailId, medications) -> {
                            var detail = builderMap
                                    .computeIfAbsent(bookingRequestId, id -> new HashMap<>())
                                    .computeIfAbsent(
                                            serviceDetailId, id -> BookingRequestModel.DaycareService.newBuilder());

                            detail.addAllMedications(MedicationConverter.INSTANCE.entityToModel(medications));
                            if (!CollectionUtils.isEmpty(medications)) {
                                detail.setMedication(MedicationConverter.INSTANCE.entityToModel(medications.get(0)));
                            }
                        });
                    });
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.DaycareService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setDaycare(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildEvaluationServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        Map<Long, Map<Long, BookingRequestModel.EvaluationService.Builder>> builderMap = new HashMap<>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            evaluationTestDetailService.listByBookingRequestId(bookingRequestIds).stream()
                    .map(EvaluationTestDetailConverter.INSTANCE::entityToModel)
                    .forEach(serviceDetail -> builderMap
                            .computeIfAbsent(serviceDetail.getBookingRequestId(), id -> new HashMap<>())
                            .computeIfAbsent(
                                    serviceDetail.getId(), id -> BookingRequestModel.EvaluationService.newBuilder())
                            .setService(serviceDetail));
        }
        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.EvaluationService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setEvaluation(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildDogWalkingServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }

        var builderMap = new HashMap<Long, Map<Long, BookingRequestModel.DogWalkingService.Builder>>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            dogWalkingServiceDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach(service -> {
                        var model = DogWalkingServiceDetailConverter.INSTANCE.entityToModel(service);
                        builderMap
                                .computeIfAbsent(model.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        model.getId(), id -> BookingRequestModel.DogWalkingService.newBuilder())
                                .setService(model);
                    });
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.DogWalkingService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setDogWalking(service)
                                .build())
                        .toList()));
    }

    private Map<Long, List<BookingRequestModel.Service>> buildGroupClassServices(
            List<Long> bookingRequestIds, Set<BookingRequestAssociatedModel> models) {
        if (CollectionUtils.isEmpty(bookingRequestIds)) {
            return Map.of();
        }

        var builderMap = new HashMap<Long, Map<Long, BookingRequestModel.GroupClassService.Builder>>();
        if (models.contains(BookingRequestAssociatedModel.SERVICE)) {
            groupClassServiceDetailService
                    .listByBookingRequestId(bookingRequestIds)
                    .forEach(service -> {
                        var model = GroupClassServiceDetailConverter.INSTANCE.entityToModel(service);
                        builderMap
                                .computeIfAbsent(model.getBookingRequestId(), id -> new HashMap<>())
                                .computeIfAbsent(
                                        model.getId(), id -> BookingRequestModel.GroupClassService.newBuilder())
                                .setService(model);
                    });
        }

        return builderMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().values().stream()
                        .map(BookingRequestModel.GroupClassService.Builder::build)
                        .map(service -> BookingRequestModel.Service.newBuilder()
                                .setGroupClass(service)
                                .build())
                        .toList()));
    }

    /**
     * Get real service type include for booking request.
     *
     * @param bookingRequestId booking request id
     * @return service type include
     */
    public int getServiceTypeInclude(long bookingRequestId) {
        int result = 0;

        // 为了防止在新增 ServiceItemType 时忘记更新这块代码，这里使用了 enum 的特性，直接在编译时报错

        for (var serviceItemType : ServiceItemEnum.values()) {
            switch (serviceItemType) {
                case GROOMING -> {
                    if (!groomingServiceDetailService
                            .listByBookingRequestId(bookingRequestId)
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                case BOARDING -> {
                    if (!boardingServiceDetailService
                            .listByBookingRequestId(bookingRequestId)
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                case DAYCARE -> {
                    if (!daycareServiceDetailService
                            .listByBookingRequestId(bookingRequestId)
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                case EVALUATION -> {
                    if (!evaluationTestDetailService
                            .listByBookingRequestId(bookingRequestId)
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                case DOG_WALKING -> {
                    if (!dogWalkingServiceDetailService
                            .listByBookingRequestId(bookingRequestId)
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                case GROUP_CLASS -> {
                    if (!groupClassServiceDetailService
                            .listByBookingRequestId(List.of(bookingRequestId))
                            .isEmpty()) {
                        result |= serviceItemType.getBitValue();
                    }
                }
                    // default -> {} // 别加 default，这里依赖 enum 特性，在编译时发现问题
            }
        }

        return result;
    }
}
