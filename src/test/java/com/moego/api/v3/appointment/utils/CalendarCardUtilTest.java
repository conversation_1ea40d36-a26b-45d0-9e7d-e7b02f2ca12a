package com.moego.api.v3.appointment.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.api.appointment.v1.CalendarCardCustomerInfo;
import com.moego.idl.api.appointment.v1.CalendarCardDraggableInfo;
import com.moego.idl.api.appointment.v1.CalendarCardPetInfo;
import com.moego.idl.api.appointment.v1.CalendarCardServiceInfo;
import com.moego.idl.api.appointment.v1.CalendarCardView;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignModel;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignView;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

class CalendarCardUtilTest {

    @Test
    void buildCalendarCardsFromBlockTimes_EmptyBlocks() {
        // Arrange

        // Act
        var result = CalendarCardUtil.buildCalendarCardsFromBlockTimes(List.of());

        // Assert
        assertThat(result).isEqualTo(List.of());
    }

    @Test
    void buildCalendarCardsFromBlockTimes() {
        // Arrange
        var blocks = List.of(BlockTimeModel.newBuilder()
                .setId(1L)
                .setStaffId(10L)
                .setStartDate("2024-10-28")
                .setStartTime(540)
                .setEndDate("2024-10-28")
                .setEndTime(1080)
                .setColorCode("#FF0000")
                .setDescription("This is a test block")
                .build());

        // Act
        var result = CalendarCardUtil.buildCalendarCardsFromBlockTimes(blocks);

        // Assert
        var expected = List.of(CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BLOCK)
                .setAppointmentId(1L)
                .setStaffId(10L)
                .setDate("2024-10-28")
                .setStartTime(540)
                .setEndDate("2024-10-28")
                .setEndTime(1080)
                .setRepeatId(0L)
                .setAppointmentColor("#FF0000")
                .setTicketComments("This is a test block")
                .setHasRelatedSplitCards(false)
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(true)
                        .setResize(true)
                        .build()));
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void buildCalendarCardsFromBookingRequests_EmptyBookingRequests() {
        // Arrange

        // Act
        var result = CalendarCardUtil.buildCalendarCardsFromBookingRequests(List.of());

        // Assert
        assertThat(result).isEqualTo(List.of());
    }

    @Test
    void buildCalendarCardsFromBookingRequests() {
        // Arrange
        var bookingRequests = List.of(BookingRequestModel.newBuilder()
                .setAppointmentId(1L)
                .setId(10L)
                .setStartDate("2024-10-28")
                .setStartTime(540)
                .setEndDate("2024-10-28")
                .setEndTime(1080)
                .setCustomerId(100L)
                .addServices(BookingRequestModel.Service.newBuilder()
                        .setGrooming(BookingRequestModel.GroomingService.newBuilder()
                                .setService(GroomingServiceDetailModel.newBuilder()
                                        .setPetId(1000L)
                                        .setId(10000L)
                                        .setServiceId(100000L)
                                        .setStartDate("2024-10-28")
                                        .setEndDate("2024-10-28")
                                        .setStartTime(540)
                                        .setEndTime(1080)
                                        .setServiceTime(60)
                                        .setServicePrice(100.0)
                                        .setStaffId(1000000L))
                                .setAutoAssign(GroomingAutoAssignModel.newBuilder()
                                        .setId(2)
                                        .setBookingRequestId(10L)
                                        .setStaffId(1000000L))))
                .build());

        // Act
        var result = CalendarCardUtil.buildCalendarCardsFromBookingRequests(bookingRequests);

        // Assert
        var expected = List.of(CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setAppointmentId(1L)
                .setBookingRequestId(10L)
                .setDate("2024-10-28")
                .setStartTime(540)
                .setEndDate("2024-10-28")
                .setEndTime(1080)
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder().setCustomerId(100L))
                .addPets(CalendarCardPetInfo.newBuilder()
                        .setPetId(1000L)
                        .addServices(CalendarCardServiceInfo.newBuilder()
                                .setPetDetailId(10000L)
                                .setServiceId(100000L)
                                .setServiceTime(60)
                                .setServicePrice(100.0)
                                .setStartTime(540)
                                .setEndTime(1080)))
                .addPetDetailIds(10000L)
                .setStaffId(1000000L)
                .setAutoAssign(GroomingAutoAssignView.newBuilder()
                        .setId(2)
                        .setBookingRequestId(10L)
                        .setStaffId(1000000L))
                .setAppointmentColor("#000000")
                .setHasRelatedSplitCards(false)
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(false)
                        .setResize(false)
                        .setNotResizeReason(CalendarCardDraggableInfo.NotResizeReason.IS_BOOKING_REQUEST)
                        .build()));
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_NotSplit() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);
        var expected = List.of(card1);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_SplitBySameDay_StartTimeLt12AM() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(1560);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);
        var card2 = CalendarCardView.newBuilder()
                .setDate("2024-10-29")
                .setStartTime(0)
                .setEndDate("2024-10-29")
                .setEndTime(120);
        var expected = List.of(card1, card2);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_SplitBySameDay_StartTimeEqual12AM() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1440)
                .setEndDate("2024-10-28")
                .setEndTime(1560);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-29")
                .setStartTime(0)
                .setEndDate("2024-10-29")
                .setEndTime(120);
        var expected = List.of(card1);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_SplitBySameDay_StartTimeGt12AM() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1560)
                .setEndDate("2024-10-28")
                .setEndTime(2040);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-29")
                .setStartTime(120)
                .setEndDate("2024-10-29")
                .setEndTime(600);
        var expected = List.of(card1);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_SplitByDifferentDay() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-29")
                .setEndTime(1320);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);
        var card2 = CalendarCardView.newBuilder()
                .setDate("2024-10-29")
                .setStartTime(0)
                .setEndDate("2024-10-29")
                .setEndTime(1320);
        var expected = List.of(card1, card2);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    void splitCardByDay_SplitByDifferentDayAndSameDay() {
        // Arrange
        var card = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-29")
                .setEndTime(1560);

        // Act
        var result = CalendarCardUtil.splitCardByDay(card);

        // Assert
        var card1 = CalendarCardView.newBuilder()
                .setDate("2024-10-28")
                .setStartTime(1320)
                .setEndDate("2024-10-28")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);
        var card2 = CalendarCardView.newBuilder()
                .setDate("2024-10-29")
                .setStartTime(0)
                .setEndDate("2024-10-29")
                .setEndTime(CalendarCardUtil.MINUTES_IN_DAY);
        var card3 = CalendarCardView.newBuilder()
                .setDate("2024-10-30")
                .setStartTime(0)
                .setEndDate("2024-10-30")
                .setEndTime(120);
        var expected = List.of(card1, card2, card3);
        assertThat(result).usingRecursiveComparison().isEqualTo(expected);
    }

    @Test
    @DisplayName("Test appointment with finished status cannot be resized")
    void testAppointmentFinished() {
        // Prepare test data
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.FINISHED)
                .build();

        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder();

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.APPOINTMENT_FINISHED);
    }

    @Test
    @DisplayName("Test appointment with multiple pets cannot be resized")
    void testMultiplePets() {
        // Prepare test data
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build();

        // Create a builder with multiple pets
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder()
                .addPets(CalendarCardPetInfo.newBuilder().build())
                .addPets(CalendarCardPetInfo.newBuilder().build());

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES);
    }

    @Test
    @DisplayName("Test appointment with multiple services cannot be resized")
    void testMultipleServices() {
        // Prepare test data
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build();

        // Create a pet with multiple services
        CalendarCardPetInfo pet = CalendarCardPetInfo.newBuilder()
                .addServices(CalendarCardServiceInfo.newBuilder().build())
                .addServices(CalendarCardServiceInfo.newBuilder().build())
                .build();

        // Create a builder with one pet that has multiple services
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder().addPets(pet);

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES);
    }

    @Test
    @DisplayName("Test appointment that can be resized - single pet with single service")
    void testResizable() {
        // Prepare test data
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build();

        // Create a pet with a single service
        CalendarCardPetInfo pet = CalendarCardPetInfo.newBuilder()
                .addServices(CalendarCardServiceInfo.newBuilder().build())
                .build();

        // Create a builder with one pet that has one service
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder().addPets(pet);

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    @DisplayName("Test appointment that can be resized - single pet with zero services")
    void testResizableWithZeroServices() {
        // Prepare test data
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build();

        // Create a pet with no services
        CalendarCardPetInfo pet = CalendarCardPetInfo.newBuilder().build();

        // Create a builder with one pet that has no services
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder().addPets(pet);

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    @DisplayName("Test condition priority - finished status takes precedence over multiple pets")
    void testPriorityFinishedOverMultiplePets() {
        // Prepare test data - satisfying multiple conditions
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.FINISHED)
                .build();

        // Create a builder with multiple pets
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder()
                .addPets(CalendarCardPetInfo.newBuilder().build())
                .addPets(CalendarCardPetInfo.newBuilder().build());

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results - should return APPOINTMENT_FINISHED reason
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.APPOINTMENT_FINISHED);
    }

    @Test
    @DisplayName("Test condition priority - multiple pets takes precedence over multiple services")
    void testPriorityMultiplePetsOverMultipleServices() {
        // Prepare test data - satisfying multiple conditions
        AppointmentModel appointment = AppointmentModel.newBuilder()
                .setStatus(AppointmentStatus.UNCONFIRMED)
                .build();

        // Create a pet with multiple services
        CalendarCardPetInfo pet = CalendarCardPetInfo.newBuilder()
                .addServices(CalendarCardServiceInfo.newBuilder().build())
                .addServices(CalendarCardServiceInfo.newBuilder().build())
                .build();

        // Create a builder with multiple pets, one with multiple services
        CalendarCardView.Builder cardBuilder = CalendarCardView.newBuilder()
                .addPets(pet)
                .addPets(CalendarCardPetInfo.newBuilder().build());

        // Execute test
        CalendarCardDraggableInfo.NotResizeReason result =
                CalendarCardUtil.getNotResizeReason(appointment, cardBuilder);

        // Verify results - should return due to multiple pets
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES);
    }

    @Test
    public void testCanResizeWithMultipleServices_EmptyServices() {
        // Arrange
        List<CalendarCardServiceInfo> services = Collections.emptyList();

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    public void testCanResizeWithMultipleServices_SingleService() {
        // Arrange
        List<CalendarCardServiceInfo> services = List.of(createService(1, 30));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    public void testCanResizeWithMultipleServices_OneNonZeroService() {
        // Arrange
        List<CalendarCardServiceInfo> services =
                Arrays.asList(createService(1, 30), createService(2, 0), createService(3, 0));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    public void testCanResizeWithMultipleServices_OneZeroServiceWithOneNonZero() {
        // Arrange
        List<CalendarCardServiceInfo> services = Arrays.asList(createService(1, 0), createService(2, 30));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    public void testCanResizeWithMultipleServices_MultipleNonZeroServices() {
        // Arrange
        List<CalendarCardServiceInfo> services =
                Arrays.asList(createService(1, 0), createService(2, 30), createService(3, 45));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES);
    }

    @Test
    public void testCanResizeWithMultipleServices_OneZeroServiceOnly() {
        // Arrange
        List<CalendarCardServiceInfo> services = List.of(createService(1, 0));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED);
    }

    @Test
    public void testCanResizeWithMultipleServices_MultipleZeroServicesOnly() {
        // Arrange
        List<CalendarCardServiceInfo> services = Arrays.asList(createService(1, 0), createService(2, 0));

        // Act
        CalendarCardDraggableInfo.NotResizeReason result = CalendarCardUtil.canResizeWithMultipleServices(services);

        // Assert
        assertThat(result).isEqualTo(CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES);
    }

    // 辅助方法：创建带有指定ID和时长的服务
    private CalendarCardServiceInfo createService(long serviceId, int serviceDuration) {
        return CalendarCardServiceInfo.newBuilder()
                .setServiceId(serviceId)
                .setServiceName("Service " + serviceId)
                .setServiceTime(serviceDuration)
                .build();
    }
}
