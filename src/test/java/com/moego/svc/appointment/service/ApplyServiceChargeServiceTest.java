package com.moego.svc.appointment.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentPetMedicationScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataName;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.order.v1.SurchargeType;
import com.moego.svc.appointment.service.remote.BusinessPetMetadataRemoteService;
import com.moego.svc.appointment.service.remote.OfferingRemoteService;
import com.moego.svc.appointment.service.remote.ServiceChargeHelper;
import com.moego.svc.appointment.utils.ServiceChargeUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ServiceCharge金额计算测试")
class ApplyServiceChargeServiceTest {

    @Mock
    private ServiceChargeHelper serviceChargeHelper;

    @Mock
    private OfferingRemoteService offeringRemoteService;

    @Mock
    private BusinessPetMetadataRemoteService businessPetMetadataRemoteService;

    @InjectMocks
    private ApplyServiceChargeService applyServiceChargeService;

    private static final long COMPANY_ID = 1001L;
    private static final long BUSINESS_ID = 2001L;

    @Nested
    @DisplayName("正常计算场景")
    class NormalCalculationScenarios {

        @Test
        @DisplayName("应该正确计算单个宠物服务费用")
        void shouldCalculateSinglePetServiceCharge() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);

            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            BigDecimal expected = BigDecimal.valueOf(150.00);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 1, 2L, 2);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(expected);

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                var foodSourceMapping = foodSourceMetadata.stream()
                        .collect(Collectors.toMap(
                                BusinessPetMetadataModel::getMetadataValue, BusinessPetMetadataModel::getId));
                assertThat(result).isEqualByComparingTo(expected);
                verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
                verify(offeringRemoteService).getServiceModels(eq(COMPANY_ID), anyList());
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges), any(), any(), any(), eq(foodSourceMapping)));
                serviceChargeUtilMock.verify(
                        () -> ServiceChargeUtil.calculateApplyAmount(eq(mockApplyCount), eq(serviceCharges)));
            }
        }

        @Test
        @DisplayName("应该正确计算多个宠物的复合服务费用")
        void shouldCalculateMultiplePetsServiceCharges() {
            // Arrange
            List<ServiceCharge> serviceCharges = createMultipleTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createMultiplePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            BigDecimal expected = BigDecimal.valueOf(450.75);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 2, 2L, 3, 3L, 1);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(expected);

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(expected);
                verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
                verify(offeringRemoteService).getServiceModels(eq(COMPANY_ID), anyList());
            }
        }

        @Test
        @DisplayName("应该正确处理包含喂食和用药安排的服务费用")
        void shouldCalculateServiceChargesWithFeedingAndMedication() {
            // Arrange
            List<ServiceCharge> serviceCharges = createFeedingMedicationServiceCharges();
            List<PetDetailDef> petDetailDefs = createPetDetailDefsWithFeedingMedication();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            BigDecimal expected = BigDecimal.valueOf(275.50);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 3, 2L, 2);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(expected);

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(expected);
            }
        }
    }

    @Nested
    @DisplayName("边界条件处理")
    class BoundaryConditionHandling {

        @Test
        @DisplayName("当服务费用列表为空时应该返回0")
        void shouldReturnZeroWhenServiceChargesIsEmpty() {
            // Arrange
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(Collections.emptyList());

            // Act
            BigDecimal result =
                    applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

            // Assert
            assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);
            verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
            // 验证没有调用其他服务
            verify(offeringRemoteService, never()).getServiceModels(anyLong(), anyList());
        }

        @Test
        @DisplayName("当服务费用列表为null时应该返回0")
        void shouldReturnZeroWhenServiceChargesIsNull() {
            // Arrange
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(null);

            // Act
            BigDecimal result =
                    applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

            // Assert
            assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);
        }

        @Test
        @DisplayName("当宠物详情列表为空时应该正确处理")
        void shouldHandleEmptyPetDetailDefs() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> emptyPetDetailDefs = Collections.emptyList();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(Collections.emptyList());

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> emptyApplyCount = Collections.emptyMap();
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(emptyApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                BigDecimal result = applyServiceChargeService.calculateServiceChargeAmount(
                        COMPANY_ID, BUSINESS_ID, emptyPetDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);
                verify(offeringRemoteService).getServiceModels(COMPANY_ID, Collections.emptyList());
            }
        }
    }

    @Nested
    @DisplayName("空值和缺失数据处理")
    class EmptyAndMissingDataHandling {

        @Test
        @DisplayName("当食物来源映射为空时应该正确处理")
        void shouldHandleEmptyFoodSourceMapping() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            List<BusinessPetMetadataModel> emptyFoodSourceMetadata = Collections.emptyList();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(emptyFoodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 1);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.valueOf(100.00));

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.valueOf(100.00));
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges), any(), any(), any(), eq(Map.of())));
            }
        }

        @Test
        @DisplayName("当服务模型查询返回空列表时应该正确处理")
        void shouldHandleEmptyServiceModels() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(Collections.emptyList());

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(Collections.emptyMap());
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);

                // 验证petDetails中的serviceItemType被设置为默认值
                var foodSourceMapping = foodSourceMetadata.stream()
                        .collect(Collectors.toMap(
                                BusinessPetMetadataModel::getMetadataValue, BusinessPetMetadataModel::getId));
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges),
                        argThat(petDetails -> petDetails.stream()
                                .allMatch(p -> p.getServiceItemType()
                                        == ServiceBriefView.getDefaultInstance()
                                                .getServiceItemType()
                                                .getNumber())),
                        any(),
                        any(),
                        eq(foodSourceMapping)));
            }
        }

        @Test
        @DisplayName("当宠物详情中没有服务列表时应该正确处理")
        void shouldHandlePetDetailDefsWithoutServices() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefsWithoutServices = Arrays.asList(PetDetailDef.newBuilder()
                    .setPetId(1001L)
                    .addAllServices(Collections.emptyList()) // 空的服务列表
                    .build());
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(Collections.emptyList());

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(Collections.emptyMap());
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                BigDecimal result = applyServiceChargeService.calculateServiceChargeAmount(
                        COMPANY_ID, BUSINESS_ID, petDetailDefsWithoutServices);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.ZERO);
                verify(offeringRemoteService).getServiceModels(COMPANY_ID, Collections.emptyList());
            }
        }
    }

    @Nested
    @DisplayName("依赖服务异常处理")
    class DependencyServiceExceptionHandling {

        @Test
        @DisplayName("当serviceChargeHelper抛出异常时应该传播异常")
        void shouldPropagateExceptionFromServiceChargeHelper() {
            // Arrange
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            RuntimeException expectedException = new RuntimeException("Service charge helper error");

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenThrow(expectedException);

            // Act & Assert
            assertThatThrownBy(() -> applyServiceChargeService.calculateServiceChargeAmount(
                            COMPANY_ID, BUSINESS_ID, petDetailDefs))
                    .isEqualTo(expectedException);

            verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
            verify(offeringRemoteService, never()).getServiceModels(anyLong(), anyList());
        }

        @Test
        @DisplayName("当offeringRemoteService抛出异常时应该传播异常")
        void shouldPropagateExceptionFromOfferingRemoteService() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            RuntimeException expectedException = new RuntimeException("Offering remote service error");

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenThrow(expectedException);

            // Act & Assert
            assertThatThrownBy(() -> applyServiceChargeService.calculateServiceChargeAmount(
                            COMPANY_ID, BUSINESS_ID, petDetailDefs))
                    .isEqualTo(expectedException);

            verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
            verify(offeringRemoteService).getServiceModels(eq(COMPANY_ID), anyList());
        }

        @Test
        @DisplayName("当getFoodSourceMapping抛出异常时应该传播异常")
        void shouldPropagateExceptionFromGetFoodSourceMapping() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            RuntimeException expectedException = new RuntimeException("Food source mapping error");

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(applyServiceChargeService.getFoodSourceMapping(COMPANY_ID)).thenThrow(expectedException);

            // Act & Assert
            assertThatThrownBy(() -> applyServiceChargeService.calculateServiceChargeAmount(
                            COMPANY_ID, BUSINESS_ID, petDetailDefs))
                    .isEqualTo(expectedException);
        }
    }

    @Nested
    @DisplayName("ServiceItemType和ServiceType字段设置验证")
    class ServiceTypeFieldSettingVerification {

        @Test
        @DisplayName("应该正确设置匹配服务的serviceItemType")
        void shouldSetCorrectServiceItemTypeForMatchedServices() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createMultipleServiceTypePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createMultipleServiceTypeServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 1);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.valueOf(100.00));

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.valueOf(100.00));

                // 验证serviceItemType被正确设置
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges),
                        argThat(petDetails -> {
                            // 验证每个petDetail的serviceItemType是否正确设置
                            return petDetails.stream().allMatch(petDetail -> {
                                // 根据serviceId查找对应的预期serviceItemType
                                long serviceId = petDetail.getServiceId().longValue();
                                if (serviceId == 1001L) {
                                    return petDetail.getServiceItemType() == ServiceItemType.GROOMING.getNumber()
                                            && petDetail.getServiceType() == ServiceType.SERVICE_VALUE;
                                } else if (serviceId == 1002L) {
                                    return petDetail.getServiceItemType() == ServiceItemType.BOARDING.getNumber()
                                            && petDetail.getServiceType() == ServiceType.SERVICE_VALUE;
                                } else if (serviceId == 1003L) {
                                    return petDetail.getServiceItemType() == ServiceItemType.DAYCARE.getNumber()
                                            && petDetail.getServiceType() == ServiceType.SERVICE_VALUE;
                                }
                                return false;
                            });
                        }),
                        any(),
                        any(),
                        any()));
            }
        }

        @Test
        @DisplayName("应该为不匹配的服务ID设置默认serviceItemType")
        void shouldSetDefaultServiceItemTypeForUnmatchedServices() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createPetDetailDefsWithUnmatchedServiceIds();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createLimitedServiceBriefViews(); // 只包含部分服务

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(Collections.emptyMap());
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert - 验证未匹配的服务使用默认值
                int defaultServiceItemType = ServiceBriefView.getDefaultInstance()
                        .getServiceItemType()
                        .getNumber();
                int defaultServiceType =
                        ServiceBriefView.getDefaultInstance().getType().getNumber();
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges),
                        argThat(petDetails -> {
                            return petDetails.stream().anyMatch(petDetail -> {
                                long serviceId = petDetail.getServiceId().longValue();
                                // 服务ID 9999L 在 services 中不存在，应该使用默认值
                                if (serviceId == 9999L) {
                                    return petDetail.getServiceItemType() == defaultServiceItemType
                                            && petDetail.getServiceType() == defaultServiceType;
                                }
                                // 服务ID 1001L 存在，应该使用对应的值
                                if (serviceId == 1001L) {
                                    return petDetail.getServiceItemType() == ServiceItemType.GROOMING.getNumber()
                                            && petDetail.getServiceType() == ServiceType.SERVICE_VALUE;
                                }
                                return true;
                            });
                        }),
                        any(),
                        any(),
                        any()));
            }
        }

        @Test
        @DisplayName("应该正确处理混合场景下的serviceItemType设置")
        void shouldHandleMixedScenarioServiceItemTypeSetting() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createMixedScenarioPetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createPartialMatchServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(Collections.emptyMap());
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert - 详细验证每个petDetail的serviceItemType设置
                int defaultServiceItemType = ServiceBriefView.getDefaultInstance()
                        .getServiceItemType()
                        .getNumber();
                int defaultServiceType =
                        ServiceBriefView.getDefaultInstance().getType().getNumber();
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges),
                        argThat(petDetails -> {
                            // 计算各种类型的数量来验证设置
                            long groomingCount = petDetails.stream()
                                    .filter(p -> p.getServiceItemType() == ServiceItemType.GROOMING.getNumber()
                                            && p.getServiceType() == ServiceType.SERVICE_VALUE)
                                    .count();
                            long boardingCount = petDetails.stream()
                                    .filter(p -> p.getServiceItemType() == ServiceItemType.BOARDING.getNumber()
                                            && p.getServiceType() == ServiceType.SERVICE_VALUE)
                                    .count();
                            long defaultCount = petDetails.stream()
                                    .filter(p -> p.getServiceItemType() == defaultServiceItemType
                                            && p.getServiceType() == defaultServiceType)
                                    .count();

                            // 验证期望的数量：1个grooming，1个boarding，1个default
                            return groomingCount == 1 && boardingCount == 1 && defaultCount == 1;
                        }),
                        any(),
                        any(),
                        any()));
            }
        }

        @Test
        @DisplayName("应该正确处理空服务列表时的serviceItemType设置")
        void shouldHandleEmptyServiceListForServiceItemTypeSetting() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createSinglePetDetailDefs();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(Collections.emptyList()); // 返回空列表

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(Collections.emptyMap());
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.ZERO);

                // Act
                applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert - 验证所有petDetail都使用默认serviceItemType
                int defaultServiceItemType = ServiceBriefView.getDefaultInstance()
                        .getServiceItemType()
                        .getNumber();
                int defaultServiceType =
                        ServiceBriefView.getDefaultInstance().getType().getNumber();
                serviceChargeUtilMock.verify(() -> ServiceChargeUtil.calculateServiceChargeApplyCounts(
                        eq(serviceCharges),
                        argThat(petDetails -> petDetails.stream()
                                .allMatch(petDetail -> petDetail.getServiceItemType() == defaultServiceItemType
                                        && petDetail.getServiceType() == defaultServiceType)),
                        any(),
                        any(),
                        any()));
            }
        }
    }

    @Nested
    @DisplayName("复杂业务场景")
    class ComplexBusinessScenarios {

        @Test
        @DisplayName("应该正确处理重复的服务ID")
        void shouldHandleDuplicateServiceIds() {
            // Arrange
            List<ServiceCharge> serviceCharges = createTestServiceCharges();
            List<PetDetailDef> petDetailDefs = createPetDetailDefsWithDuplicateServiceIds();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> mockApplyCount = Map.of(1L, 2);
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(mockApplyCount);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.valueOf(200.00));

                // Act
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.valueOf(200.00));

                // 验证对offeringRemoteService的调用使用了去重后的服务ID列表
                verify(offeringRemoteService)
                        .getServiceModels(
                                eq(COMPANY_ID),
                                argThat(serviceIds -> serviceIds.size() == 1 && serviceIds.contains(1001L)));
            }
        }

        @Test
        @DisplayName("应该正确处理大量数据的性能场景")
        void shouldHandleLargeDataSetPerformance() {
            // Arrange
            List<ServiceCharge> serviceCharges = createLargeServiceChargeList();
            List<PetDetailDef> petDetailDefs = createLargePetDetailDefsList();
            List<BusinessPetMetadataModel> foodSourceMetadata = createTestFoodSourceMetadata();
            List<ServiceBriefView> services = createLargeServiceBriefViewList();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(foodSourceMetadata);
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            try (MockedStatic<ServiceChargeUtil> serviceChargeUtilMock = mockStatic(ServiceChargeUtil.class)) {
                Map<Long, Integer> largeApplyCountMap = createLargeApplyCountMap();
                serviceChargeUtilMock
                        .when(() ->
                                ServiceChargeUtil.calculateServiceChargeApplyCounts(any(), any(), any(), any(), any()))
                        .thenReturn(largeApplyCountMap);
                serviceChargeUtilMock
                        .when(() -> ServiceChargeUtil.calculateApplyAmount(any(), any()))
                        .thenReturn(BigDecimal.valueOf(5000.00));

                // Act
                long startTime = System.currentTimeMillis();
                BigDecimal result =
                        applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);
                long endTime = System.currentTimeMillis();

                // Assert
                assertThat(result).isEqualByComparingTo(BigDecimal.valueOf(5000.00));
                assertThat(endTime - startTime).isLessThan(1000); // 性能检查：应在1秒内完成

                verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
                verify(offeringRemoteService).getServiceModels(eq(COMPANY_ID), anyList());
            }
        }
    }

    // ===============================
    // 测试数据创建辅助方法
    // ===============================

    private List<ServiceCharge> createTestServiceCharges() {
        return Arrays.asList(
                ServiceCharge.newBuilder()
                        .setId(1L)
                        .setPrice(100.00)
                        .setSurchargeType(SurchargeType.CUSTOM_FEE)
                        .build(),
                ServiceCharge.newBuilder()
                        .setId(2L)
                        .setPrice(25.00)
                        .setSurchargeType(SurchargeType.OFF_HOURS_FEE)
                        .build());
    }

    private List<ServiceCharge> createMultipleTestServiceCharges() {
        return Arrays.asList(
                ServiceCharge.newBuilder()
                        .setId(1L)
                        .setPrice(100.00)
                        .setSurchargeType(SurchargeType.CUSTOM_FEE)
                        .build(),
                ServiceCharge.newBuilder()
                        .setId(2L)
                        .setPrice(50.25)
                        .setSurchargeType(SurchargeType.FEEDING_FEE)
                        .build(),
                ServiceCharge.newBuilder()
                        .setId(3L)
                        .setPrice(200.50)
                        .setSurchargeType(SurchargeType.MEDICATION_FEE)
                        .build());
    }

    private List<ServiceCharge> createFeedingMedicationServiceCharges() {
        return Arrays.asList(
                ServiceCharge.newBuilder()
                        .setId(1L)
                        .setPrice(75.00)
                        .setSurchargeType(SurchargeType.FEEDING_FEE)
                        .build(),
                ServiceCharge.newBuilder()
                        .setId(2L)
                        .setPrice(62.75)
                        .setSurchargeType(SurchargeType.MEDICATION_FEE)
                        .build());
    }

    private List<PetDetailDef> createSinglePetDetailDefs() {
        SelectedServiceDef selectedService = SelectedServiceDef.newBuilder()
                .setServiceId(1001L)
                .addAllFeedings(Collections.emptyList())
                .addAllMedications(Collections.emptyList())
                .build();

        return Arrays.asList(PetDetailDef.newBuilder()
                .setPetId(1001L)
                .addServices(selectedService)
                .build());
    }

    private List<PetDetailDef> createMultiplePetDetailDefs() {
        SelectedServiceDef service1 =
                SelectedServiceDef.newBuilder().setServiceId(1001L).build();
        SelectedServiceDef service2 =
                SelectedServiceDef.newBuilder().setServiceId(1002L).build();

        return Arrays.asList(
                PetDetailDef.newBuilder().setPetId(1001L).addServices(service1).build(),
                PetDetailDef.newBuilder().setPetId(1002L).addServices(service2).build());
    }

    private List<PetDetailDef> createPetDetailDefsWithFeedingMedication() {
        AppointmentPetFeedingScheduleDef feeding = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("premium_food")
                .setFeedingAmount("2 Cups")
                .build();

        AppointmentPetMedicationScheduleDef medication = AppointmentPetMedicationScheduleDef.newBuilder()
                .setMedicationAmount("1 Tablespoon")
                .build();

        SelectedServiceDef serviceWithFeeding = SelectedServiceDef.newBuilder()
                .setServiceId(1001L)
                .addFeedings(feeding)
                .addMedications(medication)
                .build();

        return Arrays.asList(PetDetailDef.newBuilder()
                .setPetId(1001L)
                .addServices(serviceWithFeeding)
                .build());
    }

    private List<PetDetailDef> createPetDetailDefsWithDuplicateServiceIds() {
        SelectedServiceDef service1 =
                SelectedServiceDef.newBuilder().setServiceId(1001L).build();
        SelectedServiceDef service2 =
                SelectedServiceDef.newBuilder().setServiceId(1001L).build(); // 重复服务ID

        return Arrays.asList(PetDetailDef.newBuilder()
                .setPetId(1001L)
                .addServices(service1)
                .addServices(service2)
                .build());
    }

    private List<BusinessPetMetadataModel> createTestFoodSourceMetadata() {
        return List.of(
                BusinessPetMetadataModel.newBuilder()
                        .setMetadataValue("premium_food")
                        .setId(1L)
                        .build(),
                BusinessPetMetadataModel.newBuilder()
                        .setMetadataValue("standard_food")
                        .setId(2L)
                        .build(),
                BusinessPetMetadataModel.newBuilder()
                        .setMetadataValue("special_diet")
                        .setId(3L)
                        .build());
    }

    private List<ServiceBriefView> createTestServiceBriefViews() {
        return Arrays.asList(
                ServiceBriefView.newBuilder()
                        .setId(1001L)
                        .setServiceItemType(ServiceItemType.GROOMING)
                        .setType(ServiceType.SERVICE)
                        .setPriceUnit(ServicePriceUnit.PER_SESSION)
                        .build(),
                ServiceBriefView.newBuilder()
                        .setId(1002L)
                        .setServiceItemType(ServiceItemType.BOARDING)
                        .setType(ServiceType.SERVICE)
                        .setPriceUnit(ServicePriceUnit.PER_SESSION)
                        .build());
    }

    // 大数据量测试辅助方法
    private List<ServiceCharge> createLargeServiceChargeList() {
        List<ServiceCharge> charges = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            charges.add(ServiceCharge.newBuilder()
                    .setId((long) i)
                    .setPrice(i * 10.0)
                    .setSurchargeType(SurchargeType.CUSTOM_FEE)
                    .build());
        }
        return charges;
    }

    private List<PetDetailDef> createLargePetDetailDefsList() {
        List<PetDetailDef> petDetails = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            SelectedServiceDef service = SelectedServiceDef.newBuilder()
                    .setServiceId((long) (1000 + i))
                    .build();
            petDetails.add(PetDetailDef.newBuilder()
                    .setPetId((long) i)
                    .addServices(service)
                    .build());
        }
        return petDetails;
    }

    private List<ServiceBriefView> createLargeServiceBriefViewList() {
        List<ServiceBriefView> services = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            services.add(ServiceBriefView.newBuilder()
                    .setId((long) (1000 + i))
                    .setServiceItemType(ServiceItemType.GROOMING)
                    .build());
        }
        return services;
    }

    private Map<Long, Integer> createLargeApplyCountMap() {
        Map<Long, Integer> applyCount = new HashMap<>();
        for (int i = 1; i <= 50; i++) {
            applyCount.put((long) i, i % 5 + 1);
        }
        return applyCount;
    }

    // ===============================
    // ServiceType字段验证专用测试数据创建方法
    // ===============================

    private List<PetDetailDef> createMultipleServiceTypePetDetailDefs() {
        return Arrays.asList(
                PetDetailDef.newBuilder()
                        .setPetId(1001L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1001L)
                                .build()) // GROOMING
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1002L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1002L)
                                .build()) // BOARDING
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1003L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1003L)
                                .build()) // DAYCARE
                        .build());
    }

    private List<ServiceBriefView> createMultipleServiceTypeServiceBriefViews() {
        return Arrays.asList(
                ServiceBriefView.newBuilder()
                        .setId(1001L)
                        .setServiceItemType(ServiceItemType.GROOMING)
                        .setType(ServiceType.SERVICE)
                        .build(),
                ServiceBriefView.newBuilder()
                        .setId(1002L)
                        .setServiceItemType(ServiceItemType.BOARDING)
                        .setType(ServiceType.SERVICE)
                        .build(),
                ServiceBriefView.newBuilder()
                        .setId(1003L)
                        .setServiceItemType(ServiceItemType.DAYCARE)
                        .setType(ServiceType.SERVICE)
                        .build());
    }

    private List<PetDetailDef> createPetDetailDefsWithUnmatchedServiceIds() {
        return Arrays.asList(
                PetDetailDef.newBuilder()
                        .setPetId(1001L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1001L)
                                .build()) // 存在于services中
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1002L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(9999L)
                                .build()) // 不存在于services中
                        .build());
    }

    private List<ServiceBriefView> createLimitedServiceBriefViews() {
        return Arrays.asList(
                ServiceBriefView.newBuilder()
                        .setId(1001L)
                        .setServiceItemType(ServiceItemType.GROOMING)
                        .setType(ServiceType.SERVICE)
                        .build()
                // 故意不包含9999L的服务
                );
    }

    private List<PetDetailDef> createMixedScenarioPetDetailDefs() {
        return Arrays.asList(
                PetDetailDef.newBuilder()
                        .setPetId(1001L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1001L)
                                .build()) // 匹配GROOMING
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1002L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1002L)
                                .build()) // 匹配BOARDING
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1003L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(8888L)
                                .build()) // 不匹配，使用默认值
                        .build());
    }

    private List<ServiceBriefView> createPartialMatchServiceBriefViews() {
        return Arrays.asList(
                ServiceBriefView.newBuilder()
                        .setId(1001L)
                        .setServiceItemType(ServiceItemType.GROOMING)
                        .setType(ServiceType.SERVICE)
                        .build(),
                ServiceBriefView.newBuilder()
                        .setId(1002L)
                        .setServiceItemType(ServiceItemType.BOARDING)
                        .setType(ServiceType.SERVICE)
                        .build()
                // 故意不包含8888L的服务，让它使用默认值
                );
    }

    private List<PetDetailDef> createDetailedPetDetailDefs() {
        AppointmentPetFeedingScheduleDef feeding = AppointmentPetFeedingScheduleDef.newBuilder()
                .setFeedingSource("premium_food")
                .setFeedingAmount("2 Cups")
                .build();

        SelectedServiceDef detailedService = SelectedServiceDef.newBuilder()
                .setServiceId(1001L)
                .addFeedings(feeding)
                .build();

        return Arrays.asList(
                PetDetailDef.newBuilder()
                        .setPetId(1001L)
                        .addServices(detailedService)
                        .build(),
                PetDetailDef.newBuilder()
                        .setPetId(1002L)
                        .addServices(SelectedServiceDef.newBuilder()
                                .setServiceId(1002L)
                                .build())
                        .build());
    }

    @Nested
    @DisplayName("PER_PRICING_UNIT 场景")
    class PerPricingUnitScenarios {

        @Test
        @DisplayName("当 ServiceCharge 类型为 CUSTOM_FEE 且 ApplyType 为 PER_PRICING_UNIT 时，应根据 petId 分组并正确计算费用")
        void shouldCalculateCorrectlyForCustomFeeWithPerPricingUnit() {
            // Arrange
            List<ServiceCharge> serviceCharges = createCustomFeePerPricingUnitServiceCharges();
            List<PetDetailDef> petDetailDefs = createMultiPetPerPricingUnitDetailDefs();
            List<ServiceBriefView> services = createTestServiceBriefViews();

            when(serviceChargeHelper.listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID))
                    .thenReturn(serviceCharges);
            when(businessPetMetadataRemoteService.listPetMetadata(COMPANY_ID, BusinessPetMetadataName.FEEDING_SOURCE))
                    .thenReturn(Collections.emptyList());
            when(offeringRemoteService.getServiceModels(eq(COMPANY_ID), anyList()))
                    .thenReturn(services);

            // Act
            BigDecimal result =
                    applyServiceChargeService.calculateServiceChargeAmount(COMPANY_ID, BUSINESS_ID, petDetailDefs);

            // Assert, $10x2 = $20
            assertThat(result).isEqualByComparingTo(new BigDecimal("20.00"));
            verify(serviceChargeHelper).listAutoApplyServiceCharges(COMPANY_ID, BUSINESS_ID);
            verify(offeringRemoteService).getServiceModels(eq(COMPANY_ID), anyList());
        }
    }

    private List<ServiceCharge> createCustomFeePerPricingUnitServiceCharges() {
        return List.of(ServiceCharge.newBuilder()
                .setId(1L)
                .setPrice(10.00)
                .setSurchargeType(SurchargeType.CUSTOM_FEE)
                .setApplyType(ServiceCharge.ApplyType.PER_PRICING_UNIT)
                .setAutoApplyStatus(ServiceCharge.AutoApplyStatus.AUTO_APPLY_ENABLED)
                .addServiceItemTypes(ServiceItemType.GROOMING)
                .build());
    }

    private List<PetDetailDef> createMultiPetPerPricingUnitDetailDefs() {
        // Pet 1, Service
        SelectedServiceDef service1 = SelectedServiceDef.newBuilder()
                .setServiceId(1001L)
                .setStartDate("2025-01-01")
                .setStartTime(480)
                .setEndDate("2025-01-01")
                .setEndTime(540)
                .build();

        // Pet 2, Service
        SelectedServiceDef service2 = SelectedServiceDef.newBuilder()
                .setServiceId(1002L)
                .setServiceId(1001L)
                .setStartDate("2025-01-01")
                .setStartTime(480)
                .setEndDate("2025-01-01")
                .setEndTime(540)
                .build();

        return Arrays.asList(
                PetDetailDef.newBuilder().setPetId(2001L).addServices(service1).build(),
                PetDetailDef.newBuilder().setPetId(2002L).addServices(service2).build());
    }
}
