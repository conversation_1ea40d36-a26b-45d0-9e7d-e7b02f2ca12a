package customer

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	"github.com/MoeGolibrary/moego/backend/app/customer/repo/db/mock"
	addressrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address"
	addressmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/address/mock"
	contactrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact"
	contactmock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/contact/mock"
	customermock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer/mock"
	customerrelateddatarepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data"
	customerrelateddatamock "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer_related_data/mock"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

func TestCreateCustomerAggregate(t *testing.T) {
	t.Run("成功创建完整聚合", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		addr := &address.Address{
			Type:         customerpb.Address_PRIMARY,
			RegionCode:   "US",
			AddressLines: []string{"123 Main St"},
		}

		cont := &contact.Contact{
			GivenName:  "Jane",
			FamilyName: "Smith",
			Email:      "<EMAIL>",
		}

		relData := &customerrelateddata.CustomerRelatedData{
			Source:           "web",
			ReferralSourceID: 12345,
		}

		aggregate := &AggregateRequest{
			Customer:    customer,
			Addresses:   []*address.Address{addr},
			Contacts:    []*contact.Contact{cont},
			RelatedData: relData,
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		createdAddr := &addressrepo.Address{
			ID:         1,
			CustomerID: 1,
			Type:       customerpb.Address_PRIMARY,
		}

		createdContact := &contactrepo.Contact{
			ID:         1,
			CustomerID: 1,
			GivenName:  "Jane",
			FamilyName: "Smith",
		}

		createdRelData := &customerrelateddatarepo.CustomerRelatedData{
			ID:         1,
			CustomerID: 1,
			Source:     "web",
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)
		addressRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdAddr, nil)
		contactRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdContact, nil)
		customerRelatedDataRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdRelData, nil)

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Customer)
		require.Len(t, result.Addresses, 1)
		require.Len(t, result.Contacts, 1)
		require.NotNil(t, result.RelatedData)
	})

	t.Run("成功创建仅包含客户的聚合", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		aggregate := &AggregateRequest{
			Customer: customer,
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.NotNil(t, result.Customer)
		require.Len(t, result.Addresses, 0)
		require.Len(t, result.Contacts, 0)
		require.Nil(t, result.RelatedData)
	})

	t.Run("聚合为nil时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		result, err := logic.CreateCustomerAggregate(context.Background(), nil)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "customer is required")
	})

	t.Run("客户为nil时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		aggregate := &AggregateRequest{}

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "customer is required")
	})

	t.Run("客户创建失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		aggregate := &AggregateRequest{
			Customer: customer,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("database error"))

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Error(t, err)
	})

	t.Run("地址创建失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		addr := &address.Address{
			Type:         customerpb.Address_PRIMARY,
			RegionCode:   "US",
			AddressLines: []string{"123 Main St"},
		}

		aggregate := &AggregateRequest{
			Customer:  customer,
			Addresses: []*address.Address{addr},
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)
		addressRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("address creation failed"))

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "address creation failed")
	})

	t.Run("联系人创建失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		cont := &contact.Contact{
			GivenName:  "Jane",
			FamilyName: "Smith",
			Email:      "<EMAIL>",
		}

		aggregate := &AggregateRequest{
			Customer: customer,
			Contacts: []*contact.Contact{cont},
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)
		contactRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("contact creation failed"))

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "contact creation failed")
	})

	t.Run("相关数据创建失败时返回错误", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		relData := &customerrelateddata.CustomerRelatedData{
			Source:           "web",
			ReferralSourceID: 12345,
		}

		aggregate := &AggregateRequest{
			Customer:    customer,
			RelatedData: relData,
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)
		customerRelatedDataRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, errors.New("related data creation failed"))

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "related data creation failed")
	})

	t.Run("事务回滚场景", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		aggregate := &AggregateRequest{
			Customer: customer,
		}

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).Return(errors.New("transaction failed"))

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "transaction failed")
	})

	t.Run("空聚合请求验证", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		result, err := logic.CreateCustomerAggregate(context.Background(), nil)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "customer is required")
	})

	t.Run("空客户验证", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		aggregate := &AggregateRequest{
			Customer: nil,
		}

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.Error(t, err)
		require.Nil(t, result)
		require.Contains(t, err.Error(), "customer is required")
	})

	t.Run("只创建客户无可选实体", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		customerRepo := customermock.NewMockRepository(ctrl)
		txManager := mock.NewMockTransactionManager(ctrl)
		addressRepo := addressmock.NewMockRepository(ctrl)
		contactRepo := contactmock.NewMockRepository(ctrl)
		customerRelatedDataRepo := customerrelateddatamock.NewMockRepository(ctrl)

		logic := NewByParams(customerRepo, txManager, addressRepo, contactRepo, customerRelatedDataRepo)

		customer := &Customer{
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		aggregate := &AggregateRequest{
			Customer: customer,
		}

		createdCustomer := &Customer{
			ID:               1,
			GivenName:        "John",
			FamilyName:       "Doe",
			OrganizationID:   1,
			OrganizationType: customerpb.OrganizationRef_BUSINESS,
		}

		customerRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdCustomer.ToDB(), nil)

		txManager.EXPECT().Tx(gomock.Any(), gomock.Any()).DoAndReturn(
			func(ctx context.Context, fn func(context.Context, *gorm.DB) error) error {
				return fn(ctx, nil)
			})

		result, err := logic.CreateCustomerAggregate(context.Background(), aggregate)

		require.NoError(t, err)
		require.NotNil(t, result)
		require.Equal(t, createdCustomer.ID, result.Customer.ID)
		require.Empty(t, result.Addresses)
		require.Empty(t, result.Contacts)
		require.Nil(t, result.RelatedData)
	})
}
