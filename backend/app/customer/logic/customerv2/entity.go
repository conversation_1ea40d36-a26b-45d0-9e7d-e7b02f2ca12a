package customer

import (
	"context"
	"time"

	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"

	"github.com/MoeGolibrary/moego/backend/app/customer/logic/address"
	"github.com/MoeGolibrary/moego/backend/app/customer/logic/contact"
	customerrelateddata "github.com/MoeGolibrary/moego/backend/app/customer/logic/customer_related_data"
	customerrepo "github.com/MoeGolibrary/moego/backend/app/customer/repo/postgres/customer"
	customerutils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/random"
	customerpb "github.com/MoeGolibrary/moego/backend/proto/customer/v2"
)

type Customer struct {
	ID               int64                           `json:"id"`
	OrganizationType customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID   int64                           `json:"organization_id"`
	GivenName        string                          `json:"given_name"`
	FamilyName       string                          `json:"family_name"`
	CustomerType     customerpb.CustomerType         `json:"customer_type"`
	State            customerpb.Customer_State       `json:"state"`
	CustomFields     datatypes.JSON                  `json:"custom_fields"`
	LifeCycleID      int64                           `json:"life_cycle_id"`
	OwnerStaffID     int64                           `json:"owner_staff_id"`
	ActionStateID    int64                           `json:"action_state_id"`
	AvatarPath       string                          `json:"avatar_path"`
	CustomerCode     string                          `json:"customer_code"`
	ReferralSourceID int64                           `json:"referral_source_id"`
	DeletedTime      *time.Time                      `json:"deleted_time"`
	CreatedTime      time.Time                       `json:"created_time"`
	UpdatedTime      time.Time                       `json:"updated_time"`
}

func (c *Customer) ToDB() *customerrepo.Customer {
	return &customerrepo.Customer{
		ID:               c.ID,
		OrganizationType: c.OrganizationType,
		OrganizationID:   c.OrganizationID,
		GivenName:        c.GivenName,
		FamilyName:       c.FamilyName,
		CustomerType:     c.CustomerType,
		State:            c.State,
		CustomFields:     c.CustomFields,
		LifeCycleID:      c.LifeCycleID,
		OwnerStaffID:     c.OwnerStaffID,
		ActionStateID:    c.ActionStateID,
		AvatarPath:       c.AvatarPath,
		CustomerCode:     c.CustomerCode,
		ReferralSourceID: c.ReferralSourceID,
		DeletedTime:      c.DeletedTime,
		CreatedTime:      c.CreatedTime,
		UpdatedTime:      c.UpdatedTime,
	}
}

func (c *Customer) ToPB() *customerpb.Customer {
	if c == nil {
		return nil
	}

	var customerCustomFields map[string]any
	// 安全处理CustomFields
	if len(c.CustomFields) == 0 {
		customerCustomFields = make(map[string]any)
	} else {
		err := sonic.Unmarshal(c.CustomFields, &customerCustomFields)
		if err != nil {
			// 如果解析失败，使用空map
			customerCustomFields = make(map[string]any)
		}
	}

	log.DebugContext(context.Background(), "customerCustomFields", customerCustomFields)
	customFields, err := structpb.NewStruct(customerCustomFields)
	if err != nil {
		// 如果创建Struct失败，使用空Struct
		customFields, _ = structpb.NewStruct(make(map[string]any))
	}

	customer := &customerpb.Customer{
		Id: c.ID,
		Organization: &customerpb.OrganizationRef{
			Type: c.OrganizationType,
			Id:   c.OrganizationID,
		},
		GivenName:        c.GivenName,
		FamilyName:       c.FamilyName,
		State:            c.State,
		CustomFields:     customFields,
		LifecycleId:      c.LifeCycleID,
		OwnerStaffId:     c.OwnerStaffID,
		ActionStateId:    c.ActionStateID,
		AvatarPath:       c.AvatarPath,
		CustomerCode:     c.CustomerCode,
		ReferralSourceId: c.ReferralSourceID,
		CreateTime: &timestamppb.Timestamp{
			Seconds: c.CreatedTime.Unix(),
			Nanos:   int32(c.CreatedTime.Nanosecond()),
		},
		UpdateTime: &timestamppb.Timestamp{
			Seconds: c.UpdatedTime.Unix(),
			Nanos:   int32(c.UpdatedTime.Nanosecond()),
		},
	}
	if c.DeletedTime != nil {
		customer.DeleteTime = &timestamppb.Timestamp{
			Seconds: c.DeletedTime.Unix(),
			Nanos:   int32(c.DeletedTime.Nanosecond()),
		}
	}

	return customer
}

// ************ ListCustomers ************
type ListCustomersRequest struct {
	Filter     *ListCustomersFilter     `json:"filter"`
	Pagination *ListCustomersPagination `json:"pagination"`
	OrderBy    *ListCustomersOrderBy    `json:"order_by"`
}

type ListCustomersPagination struct {
	PageSize        int32  `json:"page_size"`
	Cursor          string `json:"cursor"`
	ReturnTotalSize bool   `json:"return_total_size"`
}

type ListCustomersOrderBy struct {
	Field     customerpb.ListCustomersRequest_Sorting_Field     `json:"field"`
	Direction customerpb.ListCustomersRequest_Sorting_Direction `json:"direction"`
}

type ListCustomersFilter struct {
	IDs               []int64                          `json:"ids"`
	OrganizationType  *customerpb.OrganizationRef_Type `json:"organization_type"`
	OrganizationID    int64                            `json:"organization_id"`
	CustomerType      customerpb.CustomerType          `json:"customer_type"`
	States            []customerpb.Customer_State      `json:"states"`
	ReferralSourceIDs []int64                          `json:"referral_source_ids"`
	OwnerStaffIDs     []int64                          `json:"owner_staff_ids"`
	LifecycleIDs      []int64                          `json:"lifecycle_ids"`
	ActionStateIDs    []int64                          `json:"action_state_ids"`
	CustomerCodes     []string                         `json:"customer_codes"`
}

type ListCustomersResponse struct {
	Customers []*Customer `json:"customers"`
	HasNext   bool        `json:"has_next"`
	NextToken string      `json:"next_token"`
	TotalSize *int64      `json:"total_size"`
}

// ************ UpdateCustomer ************
type UpdateCustomerRequest struct {
	ID               int64                     `json:"id"`
	GivenName        string                    `json:"given_name"`
	FamilyName       string                    `json:"family_name"`
	CustomFields     datatypes.JSON            `json:"custom_fields"`
	LifeCycleID      int64                     `json:"life_cycle_id"`
	OwnerStaffID     int64                     `json:"owner_staff_id"`
	ActionStateID    int64                     `json:"action_state_id"`
	AvatarPath       string                    `json:"avatar_path"`
	CustomerCode     string                    `json:"customer_code"`
	ReferralSourceID int64                     `json:"referral_source_id"`
	State            customerpb.Customer_State `json:"state"`
}

type AggregateRequest struct {
	Customer    *Customer
	Addresses   []*address.Address
	Contacts    []*contact.Contact
	RelatedData *customerrelateddata.CustomerRelatedData
}

type AggregateResponse struct {
	Customer    *Customer
	Addresses   []*address.Address
	Contacts    []*contact.Contact
	RelatedData *customerrelateddata.CustomerRelatedData
}

func (c *Customer) FromProto(pb *customerpb.Customer) {
	c.ID = pb.Id
	c.GivenName = pb.GivenName
	c.FamilyName = pb.FamilyName
	c.LifeCycleID = pb.LifecycleId
	c.OwnerStaffID = pb.OwnerStaffId
	c.ActionStateID = pb.ActionStateId
	c.AvatarPath = pb.AvatarPath
	c.ReferralSourceID = pb.ReferralSourceId
	c.CustomerCode = random.String(16)

	if pb.Organization != nil {
		c.OrganizationType = pb.Organization.Type
		c.OrganizationID = pb.Organization.Id
	}

	if pb.CustomFields != nil {
		c.CustomFields = datatypes.JSON(
			customerutils.JSONMarshalNoErr(pb.CustomFields.AsMap()))
	}
}
