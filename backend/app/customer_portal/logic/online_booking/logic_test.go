package onlinebooking

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	onlinebookingsetting "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting"
	mockob "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting/mock"
	mockstaff "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/staff/mock"
)

func TestLogic_SetOnlineBookingScript(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockObRepo := mockob.NewMockReadWriter(ctrl)
	mockStaffRepo := mockstaff.NewMockReadWriter(ctrl)

	logic := &Logic{
		obSettingRepo: mockObRepo,
		staffService:  mockStaffRepo,
	}

	css := "body { color: red; }"
	js := "console.log('hello');"
	validDatum := &SetOnlineBookingScriptDatum{
		CompanyID:  1,
		BusinessID: 2,
		StaffID:    3,
		CSS:        &css,
		JS:         &js,
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		err := logic.SetOnlineBookingScript(ctx, nil)
		assert.Error(t, err)
	})

	t.Run("invalid params - missing BusinessID", func(t *testing.T) {
		d := *validDatum
		d.BusinessID = 0
		err := logic.SetOnlineBookingScript(ctx, &d)
		assert.Error(t, err)
	})

	t.Run("invalid params - CSS and JS both nil", func(t *testing.T) {
		d := *validDatum
		d.CSS = nil
		d.JS = nil
		err := logic.SetOnlineBookingScript(ctx, &d)
		assert.Error(t, err)
	})

	t.Run("repo List error", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, errors.New("db error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})

	t.Run("create new setting", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		mockObRepo.EXPECT().Create(ctx, gomock.Any()).Return(nil)
		// mock staff service
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(&organizationpb.StaffModel{}, nil)
		mockStaffRepo.EXPECT().ListOwnerStaffInfoRequest(ctx, []int64{validDatum.CompanyID}).Return(map[int64]*organizationpb.OwnerStaffDef{
			validDatum.CompanyID: &organizationpb.OwnerStaffDef{},
		}, nil)

		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("create new setting error", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		mockObRepo.EXPECT().Create(ctx, gomock.Any()).Return(errors.New("create error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})

	t.Run("update existing setting", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  validDatum.CompanyID,
			BusinessID: validDatum.BusinessID,
			CSS:        "",
			JS:         "",
		}
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		mockObRepo.EXPECT().Update(ctx, gomock.Any()).Return(nil)
		// mock staff service
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(&organizationpb.StaffModel{}, nil)
		mockStaffRepo.EXPECT().ListOwnerStaffInfoRequest(ctx, []int64{validDatum.CompanyID}).Return(map[int64]*organizationpb.OwnerStaffDef{
			validDatum.CompanyID: &organizationpb.OwnerStaffDef{},
		}, nil)

		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("update existing setting error", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  validDatum.CompanyID,
			BusinessID: validDatum.BusinessID,
			CSS:        "",
			JS:         "",
		}
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		mockObRepo.EXPECT().Update(ctx, gomock.Any()).Return(errors.New("update error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})
}

func TestLogic_GetOnlineBookingScript(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockObRepo := mockob.NewMockReadWriter(ctrl)
	mockStaffRepo := mockstaff.NewMockReadWriter(ctrl)

	logic := &Logic{
		obSettingRepo: mockObRepo,
		staffService:  mockStaffRepo,
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		res, err := logic.GetOnlineBookingScript(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("invalid params - missing BusinessID", func(t *testing.T) {
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{})
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("repo List error", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, errors.New("db error"))
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("no settings found", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, "", res.CSS)
		assert.Equal(t, "", res.JS)
	})

	t.Run("settings found", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  1,
			BusinessID: 2,
			CSS:        "body { color: red; }",
			JS:         "console.log('hello');",
		}
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, setting.CSS, res.CSS)
		assert.Equal(t, setting.JS, res.JS)
	})
}
