package main

import (
	"context"
	"time"
	_ "time/tzdata"

	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/configloader"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/logic/jiraslareminder"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/entity"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/jira"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/repo/slack"
	"github.com/MoeGolibrary/moego/backend/app/cs_page_watcher/service"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/config/nacos"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/database/gorm"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/debuglog"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/opentelemetry"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/recovery"
	_ "github.com/MoeGolibrary/moego/backend/common/rpc/filters/validation"
	rpc "github.com/MoeGolibrary/moego/backend/common/rpc/framework"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	pb "github.com/MoeGolibrary/moego/backend/proto/cs_page_watcher/v1"
)

func main() {
	// 设置全局时区为 UTC
	time.Local = time.UTC
	s := rpc.NewServer()
	cfg := configloader.Init("./config")
	nacosCfg := configloader.InitNacosConfigAndGet(context.Background())
	log.Infof("nacosCfg: %v", nacosCfg)

	jiraClient, err := jira.NewIssueRepository(cfg.CsPageWatcher.JiraEmail, cfg.CsPageWatcher.JiraToken)
	if err != nil {
		log.Fatalf("failed to create jira client: %v", err)
	}
	slackClient := slack.NewClient(cfg.CsPageWatcher.SlackToken)

	jiraReminder := service.NewScheduledJiraReminder(
		jiraslareminder.NewSLAReminder(),
		slackClient,
		jiraClient,
		cfg.CsPageWatcher.SLAReminderChannelID)

	grpc.Register(s, &pb.CSPageWatcherService_ServiceDesc, service.NewPageWatcher(
		cfg,
		jiraClient,
		slackClient,
		entity.NewCsPageReaderWriter(),
		jiraReminder,
	))

	log.Infof("server start:%v", time.Now())
	if err := s.Serve(); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}

	log.Infof("server stopped")
}
