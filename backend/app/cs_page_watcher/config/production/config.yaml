secrets:
  - name: "moego/testing/datasource"
    prefix: "secret.datasource."
  - name: "moego/devops/nacos"
    prefix: "secret.nacos."

server:
  app: cs-page-watcher # 业务的应用名，对应 Nacos 配置的 "归属应用"
  server: devops # 进程服务名，对应 Nacos 配置的 "Group"
  filter:
    - opentelemetry
    - recovery
    - debuglog
    - validation
  service:
    - name: backend.proto.cs_page_watcher.v1.CSPageWatcherService
      ip: 0.0.0.0
      port: 9090
      protocol: grpc
      timeout: 600000
client:
  network: tcp
  protocol: grpc
  filter:
    - debuglog
  transport: grpc
  timeout: 600000
  service:
    - callee: postgres
      target: dsn://postgresql://${secret.datasource.postgres.moego_tools.username}:${secret.datasource.postgres.moego_tools.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/devops_cs_page?sslmode=disable
      protocol: gorm
      transport: gorm
      timeout: 600000
plugins:
  database:
    gorm:
      max_idle: 20
      max_open: 100
      max_lifetime: 180000 # millisecond
      service:
        - name: postgres
          max_idle: 10
          max_open: 50
          max_lifetime: 180000
  config:
    nacos:
      providers:
        - name: nacos
          enabled: true
          server_addr: ${secret.nacos.server-addr}
          username: ${secret.nacos.username}
          password: ${secret.nacos.password}
          namespace: ${secret.nacos.namespace}
  telemetry:
    opentelemetry:
      traces:
        disable_trace_body: false
  auth:
    validation:
      enable_error_log: false
